import { cloneDeep } from "lodash-es";

/**
 * @description 获取原始类型
 * @param {*} value
 * @returns {String} 类型字符串，如'String', 'Object', 'Null', 'Boolean', 'Number', 'Array'
 */
export function toRawType(value: string) {
  return Object.prototype.toString.call(value).slice(8, -1);
}

/**
 * 清理空数据
 * @param {*} config
 */
export const clearEmptyParam = (config: Record<string, any>) => {
  ["data", "params"].forEach((item) => {
    if (config[item]) {
      const keys = Object.keys(config[item]);
      if (keys.length) {
        keys.forEach((key: string) => {
          const rawType = toRawType(config[item]);
          if (["", undefined, null].includes(config[item][key]) && ["Object"].includes(rawType)) {
            // 移除属性之前，进行深拷贝断开引用，避免影响页面
            config[item] = cloneDeep(config[item]);
            delete config[item]?.[key];
          }
        });
      }
    }
  });
};

// 百度坐标系转火星坐标系(GCJ-02)
export const bdMap_to_txMap = (lat: number, lng: number) => {
  const pi = (3.14159265358979324 * 3000.0) / 180.0;
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
  const tx_lng = z * Math.cos(theta);
  const tx_lat = z * Math.sin(theta);
  return { lng: tx_lng, lat: tx_lat };
};

// 转换为wan为单位
export const toWan = (num: number, fixed = 2) => {
  return num > 0 ? (num / 10000).toFixed(fixed) + "万" : "0";
};

/**
 * 工具函数：将数字转换为“万”为单位，并保留两位小数
 * @param num - 原始客流量（如 21419800.02）
 * @returns 转换后数字（如 2142.00）
 */
export const formatToWan = (num: number): number => {
  return num ? Number((num / 10000).toFixed(2)) : 0;
};
