// 报告的配置页面，包括幻灯片配置、快速导航配置等。
import { SlideComponentsType, FooterLinkSettingType } from "./type";
import BusiOverview from "./slides/business-district-overview/index.vue";
import hourTrendOfPassenger from "./slides/trends-forecasts/hour-trend-of-passenger.vue";
import dailyTrendOfPassenger from "./slides/trends-forecasts/daily-trend-of-passenger.vue";
import dailyTrendOfPassengerQueueHolidays from "./slides/trends-forecasts/daily-trend-of-passenger-queue-holidays.vue";
import trendPredictionAnalysis from "./slides/trends-forecasts/trend-prediction-analysis.vue";
import ageAnalysis from "./slides/consumer-customer-group/age-analysis.vue";
import genderAnalysis from "./slides/consumer-customer-group/gender-analysis.vue";

import passengerFlowDuringHolidays from "./slides/trends-forecasts/passenger-flow-during-holidays.vue";
import durationStayLocalGuests from "./slides/trends-forecasts/duration-stay-local-guests.vue";
import businessDistrictPreference from "./slides/consumer-preference-analysis/business-district-preference.vue";

import marriageFertility from "./slides/customer-visit-chart/marriage-fertility.vue";
import valueAnalysis from "./slides/consumer-customer-group/value-analysis.vue";

import ageDistribution from "./slides/visitor-portrait/age-distribution.vue";
import genderDistribution from "./slides/visitor-portrait/gender-distribution.vue";
import residentialType from "./slides/distribution-visitor-groups/residential-type.vue";
import residentPopulation from "./slides/distribution-visitor-groups/resident-population.vue";
import otherPassenger from "./slides/distribution-visitor-groups/other-passenger.vue";

import travelWithin from "./slides/distribution-visitor-groups/travel-within.vue";
import residentProvince from "./slides/distribution-visitor-groups/resident-province.vue";
import publicTransportation from "./slides/trends-forecasts/public-transportation.vue";
import registeredResidenceDistribution from "./slides/trends-forecasts/registered-residence-distribution.vue";
import timePreference from "./slides/consumer-preference-analysis/time-preference.vue";
import occupationEducational from "./slides/customer-visit-chart/occupation-educational.vue";
import incomeConsumption from "./slides/customer-visit-chart/income-consumption.vue";
import formsPreference from "./slides/consumer-preference-analysis/forms-preference.vue";
import sourceDistribution from "./slides/consumer-customer-group/source-distribution.vue";
import residenceOffice from "./slides/distribution-visitor-groups/residence-office.vue";
import regionalOverview from "./slides/customer-visit-chart/regional-overview.vue";
import consumerOverview from "./slides/consumer-customer-group/consumer-overview.vue";
import trendsOverview from "./slides/trends-forecasts/trends-overview.vue";
import featureOverview from "./slides/consumer-customer-group/feature-overview.vue";
import consumptionFeatureOverview from "./slides/consumer-preference-analysis/consumption-feature-overview.vue";

/**
 * 页面配置
 * component: 组件名称
 * name: 组件标识，有地图的需要和地图中的 defineExpose 中 name 的名称一致
 * title: 组件标题， 如果是底部栏用到了，则需要写对应的标题名称
 * hasMap: 是否包含地图 (非必填)
 * group: 分组情况，对应底部导航栏 (非必填)
 * 注意：组件在slides中的顺序关系到底部导航栏中的配置（page_no），需要对应好。
 */

export const SLIDE_COMPONENTS: SlideComponentsType[] = [
  { component: regionalOverview, name: "regionalOverview", title: "区域总览" },
  { component: BusiOverview, name: "businessOverview", title: "位置分布", hasMap: true, group: 1 },
  {
    component: ageDistribution,
    name: "ageDistribution",
    title: "年龄分布",
    hasMap: true,
    group: 1
  },
  {
    component: genderDistribution,
    name: "genderDistribution",
    title: "性别分布",
    hasMap: true,
    group: 1
  },
  {
    component: marriageFertility,
    name: "marriageFertility",
    title: "婚育分布",
    group: 1
  },
  {
    component: occupationEducational,
    name: "occupationEducational",
    title: "职业学历分析",
    group: 1
  },
  {
    component: incomeConsumption,
    name: "incomeConsumption",
    title: "收入消费占比分析",
    group: 1
  },
  { component: consumerOverview, name: "consumerOverview", title: "访客概况" },
  {
    component: residentialType,
    name: "residentialType",
    title: "来源分布",
    hasMap: true,
    group: 2
  },
  {
    component: residenceOffice,
    name: "residenceOffice",
    title: "常驻人口分布"
  },
  {
    component: residentPopulation,
    name: "residentPopulation",
    title: "商圈的常驻人口",
    hasMap: true
  },
  {
    component: otherPassenger,
    name: "otherPassenger",
    title: "来自其他各市的客流",
    hasMap: true
  },

  {
    component: travelWithin,
    name: "travelWithin",
    title: "出行分布",
    hasMap: true,
    group: 2
  },
  {
    component: residentProvince,
    name: "residentProvince",
    title: "出行国内各省人口数量及占比",
    hasMap: true
  },
  {
    component: trendsOverview,
    name: "trendsOverview",
    title: "商圈客群行为特征"
  },
  {
    component: hourTrendOfPassenger,
    name: "hourTrendOfPassenger",
    title: "时间趋势及预测",
    group: 3
  },
  { component: dailyTrendOfPassenger, name: "dailyTrendOfPassenger", title: "客流分日走势" },
  {
    component: passengerFlowDuringHolidays,
    name: "passengerFlowDuringHolidays",
    title: "商圈节假日日客流"
  },
  {
    component: dailyTrendOfPassengerQueueHolidays,
    name: "dailyTrendOfPassengerQueueHolidays",
    title: "客流分日走势-剔除重要日期"
  },
  {
    component: trendPredictionAnalysis,
    name: "trendPredictionAnalysis",
    title: "客流趋势预测分析"
  },
  {
    component: durationStayLocalGuests,
    name: "durationStayLocalGuests",
    title: "本地客群停留时长",
    hasMap: true,
    group: 3
  },
  {
    component: publicTransportation,
    name: "publicTransportation",
    title: "公共交通枢纽分析",
    hasMap: true,
    group: 3
  },
  {
    component: registeredResidenceDistribution,
    name: "registeredResidenceDistribution",
    title: "商圈周边地铁站的客流户籍分布",
    hasMap: true
  },
  {
    component: featureOverview,
    name: "featureOverview",
    title: "商圈消费者特征"
  },
  {
    component: ageAnalysis,
    name: "ageAnalysis",
    title: "年龄分布",
    group: 4
  },
  {
    component: genderAnalysis,
    name: "genderAnalysis",
    title: "性别分布",
    group: 4
  },
  {
    component: sourceDistribution,
    name: "sourceDistribution",
    title: "来源分布",
    group: 4
  },
  {
    component: valueAnalysis,
    name: "valueAnalysis",
    title: "价值分析",
    group: 4
  },
  {
    component: consumptionFeatureOverview,
    name: "consumptionFeatureOverview",
    title: "商圈消费特征"
  },
  {
    component: businessDistrictPreference,
    name: "businessDistrictPreference",
    title: "商圈偏好",
    hasMap: true,
    group: 5
  },

  {
    component: timePreference,
    name: "timePreference",
    title: "时间偏好",
    hasMap: true,
    group: 5
  },
  {
    component: formsPreference,
    name: "formsPreference",
    title: "业态偏好",
    group: 5
  }
];

// 提取组件索引
const getSlideComponentsIndex = (components: SlideComponentsType[]) => {
  return components.reduce(
    (acc, cur, index) => {
      acc[cur.name] = index;
      return acc;
    },
    {} as { [key: string]: number }
  );
};

// 导出组件索引
export const SLIDE_COMPONENTS_INDEX = getSlideComponentsIndex(SLIDE_COMPONENTS);

// 分组配置
const SLIDE_GROUP_SETTING = {
  1: "到访客群画像",
  2: "到访客群分布",
  3: "到访客群行为分析",
  4: "消费客群基本特征",
  5: "消费偏好分析"
};

// 提取 footer 导航栏配置
const getFooterLinkSetting = (
  slideComponents: SlideComponentsType[],
  groupSetting: { [key: number]: string }
): FooterLinkSettingType[] => {
  let groupComponents = groupSlideComponents(slideComponents);

  return Object.keys(groupSetting).map((it) => {
    return {
      title: groupSetting[Number(it)],
      children: groupComponents[Number(it)]
    };
  });
};

// 分组
const groupSlideComponents = (components: SlideComponentsType[]) => {
  let group: { [key: number]: { title: string; page_no: number }[] } = {};

  components.forEach((item, index) => {
    if (item.group) {
      if (!group[item.group]) {
        group[item.group] = [
          {
            title: item.title,
            page_no: index
          }
        ];
      } else {
        group[item.group].push({
          title: item.title,
          page_no: index
        });
      }
    }
  });

  return group;
};

/**
 * 快速导航相关
 * page_no 代表页码,从 0 开始， 和上面 SLIDE_COMPONENTS 中的顺序对应。
 * 注意：page_no 不能重复，否则会导致导航栏的显示异常。
 *  */
export const FOOTER_LINK_SETTING: FooterLinkSettingType[] = getFooterLinkSetting(
  SLIDE_COMPONENTS,
  SLIDE_GROUP_SETTING
);
