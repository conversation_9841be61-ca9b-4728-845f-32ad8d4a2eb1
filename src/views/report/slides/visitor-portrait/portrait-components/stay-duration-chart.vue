<template>
  <div class="chart-container" style="pointer-events: auto">
    <div ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { toWan } from "@/utils";

interface ChartDataItem {
  name: string;
  value: number;
  count: number;
}

interface Props {
  chartData: ChartDataItem[];
  districtName: string;
}

const props = defineProps<Props>();

const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 颜色配置 - 根据图片中的颜色调整
const colors = [
  "#368EF9",
  "#35C6FF",
  "#57FFD5",
  "#FDF94E",
  "#FFA05D",
  "#BD46F0",
  "#FF759E",
  "#D574FF",
  "#FF93F4"
];

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value, null, { renderer: "svg" });
  updateChart();
};

// 动态计算 edgeDistance，让标签尽可能靠近饼图外延
const calculateEdgeDistance = (value: number, index: number, filterIndex: number[]) => {
  const totalValue = props.chartData.reduce((sum, item) => sum + item.value, 0);
  const percentage = value / totalValue;

  // 根据当前项的占比和位置动态调整距离
  // 占比越大，距离越近；占比越小，距离越远
  if (percentage < 0.1 && filterIndex.includes(index)) {
    // 占比很小的项，判断累计占比是否小于0.45
    const totalValue = props.chartData.reduce((sum, item) => sum + item.value, 0);
    let cumulative = 0;
    for (let i = 0; i <= index; i++) {
      if (props.chartData[i].value < 10) {
        cumulative += props.chartData[i].value / totalValue;
      }
    }
    if (cumulative < 0.35) {
      return "10%";
    }
  } else {
    return "30%";
  }
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.chartData.length) return;

  const filterData = props.chartData.map((item) => item.value);
  let filterIndex = [];
  // 遍历 filterData 将当前项的值与前一项和后一项比较，如果这三个数据都小于 10 则将这三项的index记录到 filterIndex 数组里；
  for (let i = 1; i < filterData.length - 1; i++) {
    if (filterData[i] < 10 && filterData[i - 1] < 10 && filterData[i + 1] < 10) {
      filterIndex.push(i - 1);
      filterIndex.push(i);
      filterIndex.push(i + 1);
    }
  }
  // 将 filterIndex 数组去重
  filterIndex = [...new Set(filterIndex)];

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      position: "top",
      backgroundColor: "#00305D",
      borderColor: "#00305D",
      formatter: function (params: any) {
        const { data, dataIndex } = params;
        return (
          `<span class="font-bold">${data.name}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">到访人数：</span>` +
          `<span style="color: ${colors[dataIndex]}">${toWan(data.count)}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">到访人数占比：</span>` +
          `<span style="color: ${colors[dataIndex]}">${data.value}%</span>`
        );
      },
      textStyle: {
        color: "#fff",
        fontSize: 14,
        lineHeight: 24
      }
    },

    series: [
      {
        name: "停留时长",
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 12,
          borderColor: "transparent",
          borderWidth: 0
        },
        markPoint: {
          symbol: "rect"
        },
        labelLayout: {
          hideOverlap: false,
          moveOverlap: "shiftY"
        },
        labelLine: {
          show: false
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          count: item.count,
          label: {
            show: true,
            position: "outer",
            alignTo: filterIndex.length > 0 ? "edge" : "none",
            edgeDistance:
              filterIndex.length > 0
                ? calculateEdgeDistance(item.value, index, filterIndex)
                : "30%",
            formatter: `{dot|●} ${item.name}\n${item.value}%`,
            rich: {
              dot: {
                color: colors[index % colors.length],
                fontSize: 8,
                padding: [0, 4, 0, 0]
              }
            },
            textStyle: {
              color: "#ffffff",
              fontSize: 12,
              fontWeight: 400
            }
          },
          itemStyle: {
            color: {
              type: "radial",
              x: 0,
              y: 1,
              r: 5,
              colorStops: [
                {
                  offset: 0,
                  color: colors[index % colors.length] // 中心色
                },
                {
                  offset: 0.5,
                  color: "rgba(0, 0, 0, 0.1)" // 渐变到中心色的透明度20%
                }
              ]
            }
          }
        }))
      }
    ]
  };

  chartInstance.setOption(option, true);
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  // window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 280px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
