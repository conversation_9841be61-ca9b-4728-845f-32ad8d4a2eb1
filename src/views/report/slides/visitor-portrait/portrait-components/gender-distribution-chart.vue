<template>
  <div
    ref="genderDistributionChart"
    :style="{ width: props.width + 'px', height: props.height + 'px' }"
  ></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { toWan } from "@/utils";

interface seriesType {
  value: string | number;
  percentage: string;
}

interface Props {
  width: number;
  height: number;
  xAxisData: string[];
  yAxisData: {
    male: seriesType[];
    female: seriesType[];
  };
}

const props = defineProps<Props>();

const genderDistributionChart = ref<HTMLDivElement>();

let chartInstance: echarts.EChartsType | null = null;

const initChart = () => {
  if (!genderDistributionChart.value) {
    return;
  }
  chartInstance = echarts.init(genderDistributionChart.value);

  updateChart();
};

const updateChart = () => {
  if (chartInstance) {
    const option = getChartOption() as echarts.EChartsOption;
    console.log("option", option);
    chartInstance.setOption(option);
  }
};

const getChartOption = () => {
  return {
    grid: {
      containLabel: true,
      left: 10,
      top: 30,
      right: 20,
      bottom: 30
    },
    legend: {
      height: 24,
      right: 20,
      orient: "horizontal",
      itemGap: 20,
      itemWidth: 10,
      icon: "circle",
      itemHeight: 10,
      textStyle: {
        color: "#fff",
        fontSize: 14
      },
      data: ["男性", "女性"]
    },
    tooltip: {
      position: "top",
      backgroundColor: "#00305D",
      borderColor: "#00305D",
      formatter: function (params: any) {
        return (
          `<span class="font-bold">${params.name}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">${params.seriesName}到访人数：</span>` +
          `<span class="${params.seriesName == "男性" ? "c-8298FF" : "c-FF9EBB"}">${toWan(Math.abs(params.value))}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">${params.seriesName}到访人数占比：</span>` +
          `<span class="${params.seriesName == "男性" ? "c-8298FF" : "c-FF9EBB"}">${(params.data.percentage * 100).toFixed(2)}%</span>`
        );
      },
      textStyle: {
        color: "#fff",
        fontSize: 14,
        lineHeight: 24
      }
    },
    xAxis: {
      data: props.xAxisData,
      nameGap: 0,
      silent: false,
      axisTick: { show: false },
      axisLabel: {
        color: "#fff",
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          width: 1,
          color: "rgba(255,255,255,0.2)"
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      axisLabel: {
        show: false
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: {
        show: false
      }
    },
    series: [
      {
        name: "男性",
        type: "bar",
        data: props.yAxisData.male,
        barWidth: 16,
        barGap: 48,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(130, 152, 255, 1)" },
            { offset: 1, color: "rgba(134, 153, 244, 0.76)" }
          ]),
          borderRadius: [18, 18, 18, 18]
        },
        stack: "a",
        animationDelay: function (idx: number) {
          return idx * 10;
        }
      },
      {
        name: "女性",
        type: "bar",
        data: props.yAxisData.female,
        stack: "a",
        barWidth: 16,
        barGap: 48,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(255, 155, 185, 1)" },
            { offset: 1, color: "rgba(255, 155, 185, 0.44)" }
          ]),
          borderRadius: [18, 18, 18, 18]
        },
        animationDelay: function (idx: number) {
          return idx * 10 + 100;
        }
      }
    ],
    animationEasing: "elasticOut",
    animationDelayUpdate: function (idx: number) {
      return idx * 5;
    }
  };
};

onMounted(() => {
  setTimeout(() => {
    initChart();
  }, 0);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped></style>
