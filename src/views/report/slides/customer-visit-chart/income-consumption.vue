<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-boxw-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-start items-center">
            <div class="chart-item income-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ incomeChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="570" :height="400">
                <ConcentricPieChart
                  v-if="incomeChartData.length > 0"
                  :data="incomeChartData"
                  :config="{
                    width: 570,
                    height: 400,
                    legendItemGap: 30,
                    colors: ['#53FEFF', '#FFB95A', '#84CC16', '#3B82F6', '#BB7AFC'],
                    radius: ['15%', '55%'],
                    legendDistance: 70,
                    tipTypeName: ['到访人数', '到访人数占比'],
                    legendNameWidth: 60
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item consumption-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumptionChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="570" :height="400">
                <ConcentricPieChart
                  v-if="consumptionChartData.length > 0"
                  :data="consumptionChartData"
                  :config="{
                    width: 570,
                    height: 400,
                    legendItemGap: 30,
                    colors: ['#53FEFF', '#FFB95A', '#84CC16', '#3B82F6', '#BB7AFC'],
                    radius: ['15%', '55%'],
                    legendDistance: 70,
                    tipTypeName: ['到访人数', '到访人数占比'],
                    legendNameWidth: 60
                  }"
                />
              </CardSlot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import ConcentricPieChart from "@/views/report/components/concentric-pie-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-user-profile/客群收入与消费能力占比分析.json";

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);

// 图表标题和副标题
const chartTitle = ref("客群收入与消费能力占比分析");
const chartTitleDesc = ref("");

const incomeChartTitle = ref("客群收入占比分布");
const incomeChartData = ref<any>([]);
const consumptionChartTitle = ref("客群消费能力分布");
const consumptionChartData = ref<any>([]);

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// 更新图表数据
const updateChartData = () => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (currentData) {
    incomeChartData.value = currentData.income;
    consumptionChartData.value = currentData.consumption;
  }
};

// 清理图表数据
const clearChartData = () => {
  incomeChartData.value = [];
  consumptionChartData.value = [];
};

const initData = () => {
  // 预处理所有商圈的数据，避免重复处理
  const bdsList = chartDataJson.result.bds_list;
  chartTitleDesc.value = chartDataJson.result.page_conclusion;

  bdsList.forEach((bds) => {
    const districtName = bds.bds_name;

    // 处理收入分布数据
    const incomeData = bds.income_list.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage,
      extraName: '收入'
    }));

    // 处理消费能力分布数据
    const consumptionData = bds.consumption_list.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage,
      extraName: '消费'
    }));

    // 存储处理后的数据
    processedDistrictData.value[districtName] = {
      income: incomeData,
      consumption: consumptionData
    };
  });
};

const handleTagChange = (tag: string) => {
  // 切换商圈时，先清理数据，再更新数据
  clearChartData();
  currentDistrict.value = tag;
  // 切换商圈时直接使用预处理的数据
  nextTick(() => {
    updateChartData();
  });
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["incomeConsumption"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
    updateChartData();
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});

const handleClick = () => {
  document.documentElement.requestFullscreen();
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    line-height: 18px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 6% 0;

  .chart-layout {
    margin-top: 34px;

    .chart-item {
      .chart-item-title {
        margin-bottom: 25px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }

    .consumption-chart {
      margin-left: 15%;
    }
  }
}
</style>
