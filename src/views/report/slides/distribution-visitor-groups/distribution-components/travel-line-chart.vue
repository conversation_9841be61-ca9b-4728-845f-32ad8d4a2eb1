<template>
  <el-scrollbar height="310px">
    <div class="progress-list">
      <div class="progress-item" v-for="(item, index) in data" :key="index">
        <div class="progress-item-header">
          <div class="rank-badge" :style="{ backgroundColor: getRankColor(index) }">
            {{ index + 1 }}
          </div>
          <div class="item-text">
            <div class="left-text">
              <div class="city-name">{{ item.city }}</div>
              <div class="people-count">{{ item.people }}</div>
            </div>
            <div class="people-count">{{ item.rate }}%</div>
          </div>
        </div>
        <div class="progress-line-warpper w-full">
          <div class="progress-line" :style="{ width: `${item.rate}%` }">
            <div
              class="progress-line-inner"
              :style="{ backgroundColor: getRankColor(index) }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
export interface travelLineChartType {
  city: string;
  people: string;
  rate: number;
}

interface Props {
  data: travelLineChartType[];
}

defineProps<Props>();

// -------------------------- 进度条排名颜色 --------------------------
const getRankColor = (index: number): string => {
  const colors = ["#FF90B2", "#E7C43D", "#63CCCC", "#618B89"];
  return index < 4 ? colors[index] : "#618B89";
};
</script>

<style lang="scss" scoped>
.progress-list {
  display: flex;
  flex-direction: column;
}

.progress-item {
  display: flex;
  flex-direction: column;
  color: #fff;
  padding: 8px 16px 8px 0;
}

.progress-item-header {
  display: flex;
  margin-bottom: 10px;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 30px;
  color: white;
}

.item-text {
  display: flex;
  justify-content: space-between;
  width: 85%;
}

.left-text {
  display: flex;
}

.city-name {
  font-size: 14px;
  color: #e2f6fe;
  margin-right: 49px;
}

.people-count {
  font-size: 14px;
  color: #e2f6fe;
}

.progress-line-warpper {
  height: 8px;
  margin-top: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;

  .progress-line {
    height: 8px;
    border-radius: 4px;
    .progress-line-inner {
      height: 8px;
      border-radius: 4px;
      width: 0px;
      animation: progress-line-animation 600ms ease-in-out forwards;
    }
  }
}

@keyframes progress-line-animation {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
</style>
