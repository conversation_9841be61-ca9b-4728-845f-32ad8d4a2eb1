<template>
  <div
    ref="otherPassengerChart"
    :style="{ width: props.width + 'px', height: props.height + 'px' }"
  ></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { toWan } from "@/utils";

interface Props {
  width: number;
  height: number;
  categories: string[];
  data: { value: number; percentage: number }[];
}

// 颜色配置项类型
interface ColorConfig {
  color: string;
}

// 颜色数组
const colorArray: ColorConfig[] = [
  { color: "#FF90B2" },
  { color: "#FFE995" },
  { color: "#FAFFC3" },
  { color: "#C3FFF6" },
  { color: "#B1FFBA" },
  { color: "#C3FFF6" },
  { color: "#C3E3FF" },
  { color: "#D3C3FF" }
];

const props = defineProps<Props>();
const otherPassengerChart = ref<HTMLDivElement>();

let chartInstance: echarts.EChartsType | null = null;

const initChart = () => {
  if (!otherPassengerChart.value) {
    return;
  }
  chartInstance = echarts.init(otherPassengerChart.value);
  updateChart();
};

const updateChart = () => {
  if (chartInstance) {
    const option = getChartOption() as echarts.EChartsOption;
    chartInstance.setOption(option);
  }
};

const getChartOption = () => {
  return {
    grid: {
      containLabel: true,
      left: 16,
      top: 2,
      right: 40,
      bottom: 5
    },

    tooltip: {
      position: "top",
      backgroundColor: "#00305D",
      borderColor: "#00305D",
      formatter: function (params: any) {
        return (
          `<span class="font-bold">${params.name}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">到访人数：</span>` +
          `<span class="c-E7C43D">${toWan(params.value)}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">到访人数占比：</span>` +
          `<span class="c-E7C43D">${Math.round(params.data.percentage * 100)}%</span>`
        );
      },
      textStyle: {
        color: "#fff",
        fontSize: 14,
        lineHeight: 24
      }
    },

    xAxis: {
      type: "value",
      show: false,
      position: "top",
      axisLine: { show: false, lineStyle: { color: "rgba(255, 255, 255, 0.1)" } },
      axisTick: { show: false },
      splitLine: { show: true, lineStyle: { color: "rgba(255, 255, 255, 0.05)" } },
      axisLabel: { show: true, color: "rgba(255, 255, 255, 0.6)", fontSize: 12 }
    },
    yAxis: {
      type: "category",
      inverse: true,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      axisLabel: { show: true, color: "#FFFFFF", fontSize: 14, padding: [0, 10, 0, 0] },
      data: props.categories
    },
    series: [
      {
        name: "people",
        type: "bar",
        barWidth: 10,
        barGap: "30%",
        barCategoryGap: 30,
        data: props.data,
        label: {
          show: true,
          position: "right",
          // 标签单位动态显示
          formatter: (params: any) => {
            return params.data.percentage ? Math.round(params.data.percentage * 100) + "%" : "0";
          },
          distance: 10,
          textStyle: { color: "white", fontSize: 13, fontWeight: "500" }
        },
        itemStyle: {
          color: (params: any) => {
            const num = colorArray.length;
            return colorArray[params.dataIndex % num].color;
          },
          barBorderRadius: [10, 10, 10, 10],
          borderWidth: 0.5,
          borderColor: "rgba(255, 255, 255, 0.2)"
        }
      }
    ]
  };
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped></style>
