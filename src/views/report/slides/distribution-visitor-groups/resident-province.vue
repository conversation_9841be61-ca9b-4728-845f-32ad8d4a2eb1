<template>
  <layout-main>
    <div class="w-full h-full relative">
      <!-- 地图组件 -->
      <arc-map
        ref="residentProvinceRef"
        :arcData="arcData"
        :map-options="mapOptions"
        fly-direction="toFrom"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">常驻居民出行分布</div>
        <card-slot card-type="cyan" :width="448" :height="482">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">{{ page_conclusion }}</p>
              <district-selector
                :default-active="activeDistrict"
                @district-change="handleDistrictChange"
                style="margin-bottom: 0"
              />
              <travel-line-chart :key="activeDistrict" :data="chartData" />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>
<script setup lang="ts">
import ArcMap from "../../components/arc-map.vue";
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-source-destination/客流出行各省人口占比.json";
import { useSlideStore } from "@/store/modules/slide";
import type { ArcDataItem } from "../../components/arc-map.vue";
import bdsBaseData from "@/data/common/商圈配置信息.json";
import travelLineChart, {
  travelLineChartType
} from "./distribution-components/travel-line-chart.vue";
// 基础 refs 声明
const residentProvinceRef = ref<any>(null);
const isShowDataPanel = ref(false);
const activeDistrict = ref("");
const chartTitle = ref("出行国内各省人口数量及占比");
const loaded = ref(false);
const slideStore = useSlideStore();

const mapOptions = {
  zoom: 4.4
};

// 弧线数据（匹配ArcDataItem格式）
const arcData = ref<ArcDataItem[]>([]);

const { bds_list = [], page_conclusion = "" } = chartDataJson.result;

// -------------------------- 幻灯片切换逻辑 --------------------------
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["residentProvince"]) {
    activeDistrict.value = bds_list[0].bds_name;
    isShowDataPanel.value = true;
    residentProvinceRef.value?.startAnimation();
  } else {
    activeDistrict.value = "normal";
    isShowDataPanel.value = false;
    residentProvinceRef.value?.pauseAnimation();
  }
});

watch(
  () => activeDistrict.value,
  (newVal) => {
    newVal && updateBdsData();
  }
);

const chartData = ref<travelLineChartType[]>([]);

const updateBdsData = () => {
  const currentBds = bds_list.find((item: any) => item.bds_name === activeDistrict.value);

  const bdsBase = bdsBaseData.result.bds_list.find(
    (item: any) => item.bds_name === activeDistrict.value
  );
  if (currentBds?.sec_list) {
    // 飞线数据
    let arcDataList = [];

    // 图表数据
    let data = [] as travelLineChartType[];

    for (let i = 0; i < currentBds.sec_list.length; i++) {
      const sec = currentBds.sec_list[i];
      // 占比：保留2位小数（若为小数形式，需先乘100转为百分比，如0.19→19.00%）
      const rate = (sec.sec_percentage * 100).toFixed(2);

      data.push({
        city: sec.sec_name, // 城市名称
        people: Number(sec.sec_value) + "人", // 人口（万为单位，2位小数）
        rate: Number(rate) // 占比（百分比，2位小数，转为数字用于进度条）
      });

      arcDataList.push({
        from: { lat: sec.sec_latitude, lng: sec.sec_longitude },
        to: {
          lat: bdsBase?.bds_center_latitude,
          lng: bdsBase?.bds_center_longitude
        }
      });
    }

    arcData.value = arcDataList as ArcDataItem[];
    chartData.value = data;
  }
};

// -------------------------- 商圈切换逻辑（切换后重新加载数据） --------------------------
const handleDistrictChange = (district: string) => {
  activeDistrict.value = district;
};

const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("residentProvince");
};

// -------------------------- 暴露方法 --------------------------
defineExpose({
  name: "residentProvince",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("residentProvince");
    } else {
      setTimeout(() => {
        residentProvinceRef.value?.initMap("residentProvince");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.map-section {
  width: 100%;
  height: 100%;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 0 0 10px;
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin-bottom: 12px;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 添加覆盖物悬停效果样式
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
// 进度条列表样式
.progress-list {
  height: 350px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  /* 滚动条整体样式 */
  &::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
  }

  /* 滚动条轨道（背景） */
  &::-webkit-scrollbar-track {
    // background-color: rgba(255, 255, 255, 0.05); /* 轨道颜色，与深色背景融合 */
    border-radius: 4px; /* 轨道圆角 */
  }

  /* 滚动条滑块（拖动部分） */
  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2); /* 滑块颜色，hover 时可加深 */
    border-radius: 4px; /* 滑块圆角 */
    transition: background-color 0.3s ease; /* 过渡动画，优化交互感 */
  }

  /* 滑块 hover 状态 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3); /* 加深颜色，增强反馈 */
  }

  /* 滚动条轨道两端的按钮（可选，隐藏默认按钮） */
  &::-webkit-scrollbar-button {
    display: none;
  }
}

.progress-item {
  display: flex;
  flex-direction: column;
  color: #fff;
  padding: 8px 16px 8px 0; /* 右侧留16px空白，避开滚动条 */
}
.progress-item-header {
  display: flex;
  margin-bottom: 10px;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 30px;
  color: white;
}

.item-text {
  display: flex;
  justify-content: space-between;
  width: 85%;
}
.left-text {
  display: flex;
}

.city-name {
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;
  color: #e2f6fe;
  margin-right: 49px;
}

.people-count {
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;
  color: #e2f6fe;
}

.rate-text {
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;
  color: #e2f6fe;
}

// 覆盖 Element Plus 进度条样式
:deep(.el-progress) {
  width: 100%;
  .el-progress-bar__outer {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
  }
  .el-progress-bar__inner {
    border-radius: 4px;
    transition: all 0.3s ease;
  }
}
</style>
