<template>
  <layout-main>
    <div class="w-full h-full relative">
      <!-- 地图组件 -->
      <arc-map
        ref="otherPassengerRef"
        fly-direction="fromTo"
        :arcData="arcMapData"
        :map-options="mapOptions"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">到访客群来源分布</div>
        <card-slot card-type="cyan" :width="470" :height="500">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ page_conclusion }}
              </p>
              <!-- 商圈选择器 -->
              <district-selector
                :default-active="activeDistrict"
                @district-change="handleDistrictChange"
                style="margin-bottom: 0"
              />
              <other-passenger-chart
                :key="activeDistrict"
                :width="420"
                :height="320"
                :categories="chartData.categories"
                :data="chartData.values"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import otherPassengerChart from "./distribution-components/other-passenger-chart.vue";
import ArcMap from "@/views/report/components/arc-map.vue";
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import { useSlideStore } from "@/store/modules/slide";
import chartDataJson from "@/data/visitor-source-destination/客流来源城市分布分析.json";
import bdsBaseData from "@/data/common/商圈配置信息.json";
import type { ArcDataItem } from "../../components/arc-map.vue";

const mapOptions = {
  zoom: 6.4
};

// 弧线数据
const arcMapData = ref<ArcDataItem[]>([]);

// 组件引用
const otherPassengerRef = ref<any>(null);

// 状态与数据
const isShowDataPanel = ref(false);
const loaded = ref(false);
const slideStore = useSlideStore();
const activeDistrict = ref("");
const chartTitle = ref("到访客群来源城市人口数量及占比");

const { bds_list = [], page_conclusion } = chartDataJson.result;

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["otherPassenger"]) {
    activeDistrict.value = bds_list[0].bds_name;
    isShowDataPanel.value = true;
    otherPassengerRef.value?.startAnimation();
  } else {
    activeDistrict.value = "normal";
    isShowDataPanel.value = false;
    // 暂停飞线动画
    otherPassengerRef.value?.pauseAnimation();
  }
});

watch(
  () => activeDistrict.value,
  (newVal) => {
    newVal && updateBdsData();
  }
);

interface ChartDataType {
  categories: string[];
  values: { value: number; percentage: number }[];
  rate: number[];
}
const chartData = ref<ChartDataType>({ categories: [], values: [], rate: [] });

const updateBdsData = () => {
  const currentBds = bds_list.find((item: any) => item.bds_name === activeDistrict.value);

  const bdsBase = bdsBaseData.result.bds_list.find(
    (item: any) => item.bds_name === activeDistrict.value
  );
  if (currentBds?.sec_list) {
    // 飞线数据
    let arcData = [];

    // 图表数据
    let categories = [];
    let values = [];
    let rate = [];

    for (let i = 0; i < currentBds.sec_list.length; i++) {
      const sec = currentBds.sec_list[i];

      arcData.push({
        from: { lat: sec.sec_latitude, lng: sec.sec_longitude },
        to: {
          lat: bdsBase?.bds_center_latitude,
          lng: bdsBase?.bds_center_longitude
        }
      });

      categories.push(sec.sec_name);
      values.push({
        value: sec.sec_value,
        percentage: sec.sec_percentage
      });
      rate.push(sec.sec_percentage);
    }

    arcMapData.value = arcData as ArcDataItem[];

    chartData.value = {
      categories: categories,
      values: values,
      rate: rate
    };
  } else {
    chartData.value = { categories: [], values: [], rate: [] };
  }
};

// 处理商圈切换
const handleDistrictChange = (district: string) => {
  activeDistrict.value = district;
};

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("otherPassenger");
};

// 暴露组件方法
defineExpose({
  name: "otherPassenger",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("otherPassenger");
    } else {
      setTimeout(() => {
        otherPassengerRef.value?.initMap("otherPassenger");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    margin-bottom: 12px;
    color: #e6e6e6;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }
}

.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
</style>
