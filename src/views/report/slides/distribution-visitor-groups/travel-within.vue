<template>
  <layout-main>
    <div class="w-full h-full relative">
      <arc-map
        ref="travelWithinRef"
        :arcData="arcData"
        :map-options="mapOptions"
        :center="{ lat: 32.768663, lng: 121.05862 }"
        @load="handleMapLoad"
      />

      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">常驻居民出行分布</div>
        <card-slot card-type="cyan" :width="448" :height="482">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">{{ page_conclusion }}</p>
              <district-selector
                :default-active="activeDistrict"
                @district-change="handleDistrictChange"
                style="margin-bottom: 0"
              />
            </div>
            <travel-line-chart :key="activeDistrict" :data="chartData" />
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import ArcMap from "../../components/arc-map.vue";
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-source-destination/客流出行各市人口占比.json";
import { useSlideStore } from "@/store/modules/slide";
import type { ArcDataItem } from "../../components/arc-map.vue";
import bdsBaseData from "@/data/common/商圈配置信息.json";
import travelLineChart, {
  travelLineChartType
} from "./distribution-components/travel-line-chart.vue";

const travelWithinRef = ref<any>(null);
const isShowDataPanel = ref(false);
const activeDistrict = ref("");
const chartTitle = ref("出行本省各市人口数量及占比");
const loaded = ref(false);
const slideStore = useSlideStore();

// 弧线数据（匹配ArcDataItem格式）
const arcData = ref<ArcDataItem[]>([
  { from: { lat: 31.31331, lng: 120.69778 }, to: { lat: 31.23037, lng: 121.4737 } },
  { from: { lat: 31.31331, lng: 120.69778 }, to: { lat: 32.05838, lng: 118.79647 } },
  { from: { lat: 31.31331, lng: 120.69778 }, to: { lat: 32.38987, lng: 119.41222 } },
  { from: { lat: 31.31331, lng: 120.69778 }, to: { lat: 31.98724, lng: 120.89416 } },
  { from: { lat: 31.31331, lng: 120.69778 }, to: { lat: 34.5917, lng: 119.1787 } }
]);

const mapOptions = {
  zoom: 7.5
};

const { bds_list = [], page_conclusion = "" } = chartDataJson.result;
// -------------------------- 幻灯片切换逻辑 --------------------------
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["travelWithin"]) {
    activeDistrict.value = bds_list[0].bds_name;
    isShowDataPanel.value = true;
    travelWithinRef.value?.startAnimation();
  } else {
    activeDistrict.value = "normal";
    isShowDataPanel.value = false;
    travelWithinRef.value?.pauseAnimation();
  }
});

watch(
  () => activeDistrict.value,
  (newVal) => {
    newVal && updateBdsData();
  }
);

const chartData = ref<travelLineChartType[]>([]);

const updateBdsData = () => {
  const currentBds = bds_list.find((item: any) => item.bds_name === activeDistrict.value);

  const bdsBase = bdsBaseData.result.bds_list.find(
    (item: any) => item.bds_name === activeDistrict.value
  );
  if (currentBds?.sec_list) {
    // 飞线数据
    let arcData = [];
    // 图表数据
    let data = [] as travelLineChartType[];
    for (let i = 0; i < currentBds.sec_list.length; i++) {
      const sec = currentBds.sec_list[i];
      // 占比：保留2位小数（若为小数形式，需先乘100转为百分比，如0.19→19.00%）
      const rate = (sec.sec_percentage * 100).toFixed(2);

      data.push({
        city: sec.sec_name, // 城市名称
        people: Number(sec.sec_value) + "人",
        rate: Number(rate) // 占比（百分比，2位小数，转为数字用于进度条）
      });

      arcData.push({
        from: { lat: sec.sec_latitude, lng: sec.sec_longitude },
        to: {
          lat: bdsBase?.bds_center_latitude,
          lng: bdsBase?.bds_center_longitude
        }
      });
    }
    chartData.value = data;
  }
};

// -------------------------- 商圈切换逻辑（切换后重新加载数据） --------------------------
const handleDistrictChange = (district: string) => {
  activeDistrict.value = district;
};

// -------------------------- 地图加载回调 --------------------------
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("travelWithin");
};

// -------------------------- 暴露方法 --------------------------
defineExpose({
  name: "travelWithin",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("travelWithin");
    } else {
      setTimeout(() => {
        travelWithinRef.value?.initMap("常驻居民出行地图");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
/* 原有样式保持不变 */

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

/* 其他原有样式（进度条、滚动条等）保持不变 */
.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 0 0 10px;
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin-bottom: 12px;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }
}
</style>
