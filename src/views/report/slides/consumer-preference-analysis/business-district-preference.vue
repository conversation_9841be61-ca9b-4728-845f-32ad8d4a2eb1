<template>
  <layout-main>
    <div class="w-full h-full relative">
      <bds-map
        ref="businessDistrictMapRef"
        :showBubble="true"
        show-name
        :bubble-data="processedData.bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 title="到访客群行为分析" -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">消费偏好分析</div>
        <card-slot card-type="purple" :width="525" :height="481">
          <div class="chart-content">
            <!-- 图表组件 -->
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">消费到访转化率</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData?.result?.page_conclusion }}
              </p>
              <echartsBarChart
                ref="echartsChart"
                :x-axis-data="processedData.xAxisData"
                :consume-data="processedData.consumeData"
                :visit-data="processedData.visitData"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import { BubbleData } from "@/views/report/hooks/useBdsMarkerHook";
import CardSlot from "@/views/report/components/card-slot.vue";
import echartsBarChart from "@/views/report/components/echartsBarChart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import consumer from "@/data/consumer-preference-analysis/消费人群占比.json";
import { useSlideStore } from "@/store/modules/slide";
import { toWan } from "@/utils";

interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_latitude: number;
  bds_longitude: number;
  visit_count: number;
  consume_count: number;
  percentage: number;
  bds_conclusion: string;
}
const slideStore = useSlideStore();

const businessDistrictMapRef = ref<any>(null);
const isShowDataPanel = ref(false);
const echartsChart = ref<any>(null);

// 响应式数据
const stayDurationData = ref(consumer);
const processedData = ref({
  xAxisData: [] as string[],
  consumeData: [] as number[],
  visitData: [] as number[],
  bubbleData: [] as BubbleData[]
});

// 集中处理所有数据
const processData = () => {
  const { bds_list } = stayDurationData.value.result;

  // 按 percentage 降序排序
  const sortedBdsList = [...bds_list].sort((a, b) => Number(b.percentage) - Number(a.percentage));

  // 计算总访问量和消费量
  const totalVisit = sortedBdsList.reduce((sum, item) => sum + Number(item.visit_count), 0);
  const totalConsume = sortedBdsList.reduce((sum, item) => sum + Number(item.consume_count), 0);

  // 处理X轴数据
  const xAxisData = sortedBdsList.map((item) => {
    if (item.bds_name === "文化博览中心") {
      return "文化博览\n中心";
    }
    if (item.bds_name && item.bds_name.length > 4) {
      const name = item.bds_name;
      const mid = Math.ceil(name.length / 2);
      return name.substring(0, mid) + "\n" + name.substring(mid);
    }
    return item.bds_name;
  });

  // 处理图表数据（百分比）
  const consumeData = sortedBdsList.map((item) => {
    const percentage = totalConsume > 0 ? (Number(item.consume_count) / totalConsume) * 100 : 0;
    return parseFloat(percentage.toFixed(2));
  });

  const visitData = sortedBdsList.map((item) => {
    const percentage = totalVisit > 0 ? (Number(item.visit_count) / totalVisit) * 100 : 0;
    return parseFloat(percentage.toFixed(2));
  });

  const bubbleData = getBubbleData(sortedBdsList) as BubbleData[];

  // 更新处理后的数据
  processedData.value = {
    xAxisData,
    consumeData,
    visitData,
    bubbleData
  };
};

const getBubbleData = (data: BusinessDistrict[]) => {
  return data.map((item: BusinessDistrict, i: number) => {
    const value = toWan(item.consume_count);
    return {
      bds_id: item.bds_id,
      value: value,
      size: i == 0 ? 100 : i == 1 ? 90 : i == 2 ? 80 : 60, // 前三的大小及颜色各异，后面的大小统一，颜色统一
      position: {
        lat: item.bds_latitude,
        lng: item.bds_longitude
      }
    };
  });
};

useSlideHook(async (slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["businessDistrictPreference"]) {
    isShowDataPanel.value = true;
    processData();

    // 等待数据更新和DOM更新后再初始化地图
    nextTick(() => {
      // 确保图表组件已挂载后再初始化
      setTimeout(() => {
        if (processedData.value.xAxisData.length > 0 && echartsChart.value) {
          echartsChart.value.initChart();
        }
      }, 100);
    });
  } else {
    isShowDataPanel.value = false;
    if (echartsChart.value?.myChart()) {
      echartsChart.value.myChart().dispose();
    }
    businessDistrictMapRef.value?.clearMap();
  }
});

const loaded = ref(false);

const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("businessDistrictPreference");
};

defineExpose({
  name: "businessDistrictPreference",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("businessDistrictPreference");
    } else {
      setTimeout(() => {
        businessDistrictMapRef.value?.initMap("消费到访转化率");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.map-section {
  width: 100%;
  height: 100%;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 26px 17px 0 10px;
    box-sizing: border-box;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;
    padding-left: 16px;
    box-sizing: border-box;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-family: Verdana;
      font-size: 16px;
      font-weight: bold;
      line-height: 16px;
      letter-spacing: 0em;
      color: #f7fffe;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    font-variation-settings: "opsz" auto;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0px 4px 6px #4d429e;
    padding-left: 16px;
    padding-right: 50px;
    box-sizing: border-box;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
