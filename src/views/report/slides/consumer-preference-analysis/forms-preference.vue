<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-boxw-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-justify-between items-center">
            <div class="chart-item consumer-count-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumerCountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="consumerCountChartData.length > 0"
                  :data="consumerCountChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    legendItemGap: 20,
                    colors: ['#FFA24B', '#BB7AF9', '#C7FE72', '#FFEE5A', '#53FEFF', '#53FEFF'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['消费人数', '消费人数占比'],
                    roseType: 'radius',
                    legendNameWidth: 70
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item transaction-count-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ transactionCountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <BarLineChart
                  v-if="transactionCountBarData?.length > 0"
                  :bar-data="transactionCountBarData"
                  :line-data="transactionCountLineData"
                  :bar-colors="['#50CDFF']"
                  :value-color-index="0"
                  :enable-3d="false"
                  :width="'100%'"
                  :height="'100%'"
                  barDataName="次"
                  percentageField="percentage"
                  :isTenThousandUnits="false"
                  :tip-type-name="['消费笔数', '消费笔数占比']"
                  :line-color="'#F8FF6C'"
                  :custom-options="{
                    grid: {
                      left: '15%',
                      right: '15%',
                      bottom: '12%',
                      top: '18%'
                    },
                    legend: {
                      show: false
                    }
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item consumption-amount-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumptionAmountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <BarLineChart
                  v-if="consumptionAmountBarData?.length > 0"
                  :bar-data="consumptionAmountBarData"
                  :line-data="consumptionAmountLineData"
                  :bar-colors="['#FFEB68']"
                  :bar3DColors="{
                    left: 'rgba(255, 235, 104, 0.7)',
                    right: 'rgba(255, 235, 104, 0.9)',
                    topFill: '#FFEB68'
                  }"
                  :gradient-colors="{
                    top: 'rgba(255, 235, 104, 0)',
                    bottom: 'rgba(47, 41, 1, 0.8)'
                  }"
                  barDataName="万元"
                  :y-left-name-padding="[0, 0, 10, -30]"
                  :y-right-name-padding="[0, 0, 10, 30]"
                  :value-color-index="0"
                  :enable-3d="false"
                  :width="'100%'"
                  :height="'100%'"
                  percentageField="percentage"
                  :tip-type-name="['消费金额', '消费金额占比']"
                  :line-color="'#00FBFD'"
                  :custom-options="{
                    grid: {
                      left: '15%',
                      right: '15%',
                      bottom: '12%',
                      top: '18%'
                    },
                    legend: {
                      show: false
                    }
                  }"
                />
              </CardSlot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import ConcentricPieChart from "@/views/report/components/concentric-pie-chart.vue";
import BarLineChart from "@/views/report/components/bar-line-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/consumer-preference-analysis/业态偏好-人次-笔数-金额分析.json";

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);

// 图表标题和副标题
const chartTitle = ref("业态偏好分析");
const chartTitleDesc = ref("");

const consumerCountChartTitle = ref("消费人数占比");
const consumerCountChartData = ref<any>([]);
const transactionCountChartTitle = ref("消费笔数占比");
const transactionCountBarData = ref<any>([]);
const transactionCountLineData = ref<any>([]);
const consumptionAmountChartTitle = ref("消费金额占比");
const consumptionAmountBarData = ref<any>([]);
const consumptionAmountLineData = ref<any>([]);

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// 更新图表数据
const updateChartData = () => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (currentData) {
    consumerCountChartData.value = currentData.consumerCount;
    transactionCountBarData.value = currentData.transactionCountBar;
    transactionCountLineData.value = currentData.transactionCountLine;
    consumptionAmountBarData.value = currentData.consumptionAmountBar;
    consumptionAmountLineData.value = currentData.consumptionAmountLine;
  }
};

// 清理图表数据
const clearChartData = () => {
  consumerCountChartData.value = [];
  transactionCountBarData.value = [];
  transactionCountLineData.value = [];
  consumptionAmountBarData.value = [];
  consumptionAmountLineData.value = [];
};

const initData = () => {
  // 预处理所有商圈的数据，避免重复处理
  const bdsList = chartDataJson.result.bds_list;
  chartTitleDesc.value = chartDataJson.result.page_conclusion;

  bdsList.forEach((bds: any) => {
    const districtName = bds.bds_name;

    // 优化：同一个数组只遍历一次
    // 消费人数数据
    const consumerCountData = bds.consume_amount.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage
    }));

    // 消费笔数数据
    const transactionCountBarData: any[] = [];
    const transactionCountLineData: any[] = [];
    bds.consume_user_count.forEach((item: any) => {
      transactionCountBarData.push({
        name: item.sec_name,
        value: item.sec_value,
        count: item.sec_value,
        percentage: (item.sec_percentage * 100).toFixed(2)
      });
      transactionCountLineData.push({
        name: item.sec_name,
        value: (item.sec_percentage * 100).toFixed(2)
      });
    });

    // 消费金额数据
    const consumptionAmountBarData: any[] = [];
    const consumptionAmountLineData: any[] = [];
    bds.consume_count.forEach((item: any) => {
      consumptionAmountBarData.push({
        name: item.sec_name,
        value: item.sec_value,
        count: item.sec_value,
        percentage: (item.sec_percentage * 100).toFixed(2)
      });
      consumptionAmountLineData.push({
        name: item.sec_name,
        value: (item.sec_percentage * 100).toFixed(2)
      });
    });

    // 存储处理后的数据
    processedDistrictData.value[districtName] = {
      consumerCount: consumerCountData,
      transactionCountBar: transactionCountBarData,
      transactionCountLine: transactionCountLineData,
      consumptionAmountBar: consumptionAmountBarData,
      consumptionAmountLine: consumptionAmountLineData
    };
  });
};

const handleTagChange = (tag: string) => {
  // 切换商圈时，先清理数据，再更新数据
  clearChartData();
  currentDistrict.value = tag;
  // 切换商圈时直接使用预处理的数据
  nextTick(() => {
    updateChartData();
  });
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["formsPreference"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    line-height: 18px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 34px;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }
  }
}
</style>
