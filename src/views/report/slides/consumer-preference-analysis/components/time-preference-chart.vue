<template>
  <div class="chart-container" style="pointer-events: auto">
    <div ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { toWan } from "@/utils";

interface ChartDataItem {
  name: string;
  value: number;
  count: number;
}

interface Props {
  chartData: ChartDataItem[];
  districtName: string;
}

const props = defineProps<Props>();

const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 严格还原参考图配色（按顺序对应各分段）
const colors = [
  "#FFC800", // 17-22点
  "#57FFD5", // 14-17点
  "#35C6FF", // 11-14点
  "#FF759E", // 9-11点
  "#368EF9" // 22-次日9点
];

const initChart = () => {
  if (!chartRef.value) return;
  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || !props.chartData.length) return;

  // 预生成 rich 配置
  const richConfig: Record<string, any> = {};
  props.chartData.forEach((_, index) => {
    richConfig[`valueStyle${index}`] = {
      fontSize: 12,
      fontWeight: "bold",
      color: colors[index % colors.length]
    };
  });

  const option: echarts.EChartsOption = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      position: "top",
      backgroundColor: "#00305D",
      borderColor: "#00305D",
      formatter: function (params: any) {
        return (
          `<span class="font-bold">${params.name}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">消费人数：</span>` +
          `<span class="c-FFD955">${toWan(Math.abs(params.data.count))}</span>` +
          "<br/>" +
          `<span class="c-E2F6FE">消费人数占比：</span>` +
          `<span class="c-FFD955">${params.data.value}%</span>`
        );
      },
      textStyle: {
        color: "#fff",
        fontSize: 14,
        lineHeight: 28
      }
    },
    series: [
      {
        name: "停留时长",
        type: "pie",
        radius: ["40%", "54%"],
        center: ["50%", "50%"],
        clockwise: true,
        label: {
          show: true,
          position: "outside",
          formatter: (params: any) => {
            // 为每个数据项使用对应的 rich 样式
            return [
              `{nameStyle|${params.name}}`,
              `{valueStyle${params.dataIndex}|${params.value}%}`
            ].join("\n");
          },
          rich: {
            nameStyle: {
              fontSize: 14,
              color: "#fff",
              lineHeight: 18,
              align: "left"
            },
            // 动态生成的 rich 配置
            ...richConfig
          }
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 30,
          lineStyle: {
            width: 2,
            opacity: 0.8
          }
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          count: item.count,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };

  chartInstance.setOption(option, true);
};

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  chartInstance?.dispose();
});
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
