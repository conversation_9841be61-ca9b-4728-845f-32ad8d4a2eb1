<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>

      <div class="middle">
        <div class="title">{{ chartTitle }}</div>
        <div class="subtitle">{{ chartSubtitle }}</div>
        <div class="w-full">
          <!-- 商业区标签导航 -->
          <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
        </div>
        <div class="chart-box">
          <div class="left-chart-box">
            <div class="chart-box-title-box">
              <img src="@/assets/icon.png" alt="" class="icon-img" />
              <div class="icon-title">消费人次占比</div>
            </div>
            <card-slot :width="464" :height="400" card-type="purple">
              <genderPieChart :data="consume_count" ref="genderPieChartCount" />
            </card-slot>
          </div>
          <div class="right-chart-box">
            <div class="chart-box-title-box">
              <img src="@/assets/icon.png" alt="" class="icon-img" />
              <div class="icon-title">消费金额占比</div>
            </div>
            <card-slot :width="464" :height="400" card-type="purple">
              <genderPieChart :data="consume_amount" ref="genderPieChartAmount" />
            </card-slot>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import consumer from "@/data/consumer-basic-traits/商圈消费客群性别分析.json";
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import genderPieChart from "@/views/report/components/genderPieChart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";

// 引用
const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);
const genderPieChartCount = ref<any>(null);
const genderPieChartAmount = ref<any>(null);
const chartData = ref<any>(null);

// 图表数据
const consume_count = ref<any>([]);
const consume_amount = ref<any>([]);

// 图表标题和副标题
const chartTitle = ref("消费客群性别分布分析");
const chartSubtitle = ref("");

// 统一数据处理方法
const processDistrictData = (districtData: any) => {
  // 直接解构，因为数据结构已确定
  const { consume_count: countData, consume_amount: amountData } = districtData;

  // 处理性别消费人次数据
  consume_count.value = countData.map((item: any) => ({
    name: item.sex,
    value: item.count
  }));

  // 处理性别消费金额数据
  consume_amount.value = amountData.map((item: any) => ({
    name: item.sex,
    value: item.amount
  }));
};

// 获取并处理数据
const fetchChartData = async () => {
  try {
    // 直接使用确定结构的数据，减少不必要的判断
    const { result } = consumer;
    chartData.value = result;

    if (result?.bds_list?.length > 0) {
      // 设置副标题
      chartSubtitle.value = result.page_conclusion;

      // 处理默认商圈数据（第一个）
      processDistrictData(result.bds_list[0]);
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 切换商圈处理
const handleTagChange = (value: string) => {
  currentDistrict.value = value;

  // 查找对应商圈数据
  const targetDistrict = chartData.value.bds_list.find((item: any) => item.bds_name === value);

  if (targetDistrict) {
    processDistrictData(targetDistrict);
  }
};

// 双击全屏
const handleClick = () => {
  document.documentElement.requestFullscreen();
};

// 使用slide hook
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["genderAnalysis"]) {
    fetchChartData();

    genderPieChartCount.value?.initChart();
    genderPieChartAmount.value?.initChart();
  } else {
    genderPieChartCount.value?.myChart()?.dispose();
    genderPieChartAmount.value?.myChart()?.dispose();
  }
});

// 生命周期
onMounted(() => {});
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}
.relative {
  position: relative;
  width: 100%;
  .middle {
    position: absolute;
    top: 86px;
    left: 0;
    text-align: left;
    width: 100%;
    padding: 0 7%;
    // padding: 0 0 0 10%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      font-family: Verdana;
      font-size: 26px;
      font-weight: bold;
      line-height: 26px;
      letter-spacing: 0em;
      color: #b1fff2;
      margin-bottom: 19px;
    }
    .subtitle {
      font-family: Verdana;
      font-size: 14px;
      font-weight: normal;
      line-height: 18px;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      color: #ffffff;
      max-width: 710px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 12px;
    }
    .chart-box {
      width: 100%;
      display: flex;
      margin-top: 30px;

      .chart-box-title-box {
        display: flex;
        margin-bottom: 30px;
        .icon-img {
          width: 17.82px;
          height: 23.35px;
          margin: 0 12.18px 0 0 !important;
        }
        .icon-title {
          font-family: Verdana;
          font-size: 16px;
          font-weight: bold;
          letter-spacing: 0em;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
          margin-top: 2px;
        }
      }
      .right-chart-box {
        margin-left: 230px;
      }
    }
  }
}
.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
</style>
