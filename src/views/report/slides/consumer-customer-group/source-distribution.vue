<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-boxw-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-justify-between items-center">
            <div class="chart-item consume-amount-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumeAmountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <StayDurationChart
                  v-if="consumeAmountChartData.length > 0"
                  :chart-data="consumeAmountChartData"
                  :district-name="consumeAmountChartData.map((item) => item.name)"
                  :colors="['#53FEFF', '#489BFF', '#C942FF']"
                  :label="{
                    show: false
                  }"
                  :legend="{
                    show: true,
                    icon: 'circle',
                    position: 'right',
                    distance: 20,
                    itemGap: 30,
                    isFormatterValue: true,
                    nameWidth: 60
                  }"
                  :tip-type-name="['消费金额', '消费金额占比']"
                />
              </CardSlot>
            </div>
            <div class="chart-item consume-count-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumeCountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <StayDurationChart
                  v-if="consumeCountChartData.length > 0"
                  :chart-data="consumeCountChartData"
                  :district-name="consumeCountChartData.map((item) => item.name)"
                  :colors="['#53FEFF', '#489BFF', '#C942FF']"
                  :label="{
                    show: false
                  }"
                  :legend="{
                    show: true,
                    icon: 'circle',
                    position: 'right',
                    distance: 20,
                    itemGap: 30,
                    isFormatterValue: true,
                    nameWidth: 60
                  }"
                  :tip-type-name="['消费笔数', '消费笔数占比']"
                />
              </CardSlot>
            </div>
            <div class="chart-item consumer-count-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumerCountChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <StayDurationChart
                  v-if="consumerCountChartData.length > 0"
                  :chart-data="consumerCountChartData"
                  :district-name="consumerCountChartData.map((item) => item.name)"
                  :colors="['#53FEFF', '#489BFF', '#C942FF']"
                  :label="{
                    show: false
                  }"
                  :legend="{
                    show: true,
                    icon: 'circle',
                    position: 'right',
                    distance: 20,
                    itemGap: 30,
                    isFormatterValue: true,
                    nameWidth: 60
                  }"
                  :tip-type-name="['消费人数', '消费人数占比']"
                />
              </CardSlot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import StayDurationChart from "./curr-components/ring-no-line-legend.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/consumer-basic-traits/商圈消费客群来源分析.json";

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);

// 图表标题和副标题
const chartTitle = ref("消费客群来源分布分析");
const chartTitleDesc = ref("");

const consumeAmountChartTitle = ref("消费金额占比");
const consumeAmountChartData = ref<any>([]);
const consumeCountChartTitle = ref("消费笔数占比");
const consumeCountChartData = ref<any>([]);
const consumerCountChartTitle = ref("消费人数占比");
const consumerCountChartData = ref<any>([]);

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// 更新图表数据
const updateChartData = () => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (currentData) {
    consumeAmountChartData.value = currentData.consumeAmount;
    consumeCountChartData.value = currentData.consumeUserCount;
    consumerCountChartData.value = currentData.consumerCount;
  }
};

// 清理图表数据
const clearChartData = () => {
  consumeAmountChartData.value = [];
  consumeCountChartData.value = [];
  consumerCountChartData.value = [];
};

const initData = () => {
  // 预处理所有商圈的数据，避免重复处理
  const bdsList = chartDataJson.result.bds_list;
  chartTitleDesc.value = chartDataJson.result.page_conclusion;

  bdsList.forEach((bds) => {
    const districtName = bds.bds_name;

    // 处理消费金额数据
    const consumeAmountData = bds.consume_amount.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage * 100
    }));

    // 处理消费笔数数据
    const consumeUserCountData = bds.consume_user_count.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage * 100
    }));

    // 处理消费人数数据
    const consumerCountData = bds.consumer_count.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage * 100
    }));

    // 存储处理后的数据
    processedDistrictData.value[districtName] = {
      consumeAmount: consumeAmountData,
      consumeUserCount: consumeUserCountData,
      consumerCount: consumerCountData
    };
  });
};

const handleTagChange = (tag: string) => {
  // 切换商圈时，先清理数据，再更新数据
  clearChartData();
  currentDistrict.value = tag;
  // 切换商圈时直接使用预处理的数据
  nextTick(() => {
    updateChartData();
  });
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["sourceDistribution"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    line-height: 18px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 34px;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }
  }
}
</style>
