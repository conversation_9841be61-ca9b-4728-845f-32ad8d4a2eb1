<template>
  <div class="ring-chart-container" style="pointer-events: auto; width: 100%; height: 100%">
    <div ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

interface ChartDataItem {
  name: string;
  value: number;
  percentage: string | number;
}

interface LabelConfig {
  show: boolean;
  calculateField?: "value" | "percentage"; // 计算字段：value为数值，percentage为百分比
  formatter?: string; // 自定义标签格式
}

interface LegendConfig {
  show: boolean;
  icon?: string; // 图例图标
  position?: "top" | "bottom" | "left" | "right"; // 图例位置
  distance?: number; // 图例距离图表的距离
  itemGap?: number; // 图例项之间的间距
  isFormatterValue?: boolean; // 是否格式化数值
  nameWidth?: number; // 图例名称区域宽度
}

interface Props {
  chartData: ChartDataItem[];
  colors?: string[]; // 自定义颜色数组
  label?: LabelConfig; // 标签配置
  legend?: LegendConfig; // 图例配置
  isTenThousandUnits?: boolean; // 是否是十万单位
  tipTypeName?: string[]; // 提示类型名称
  beforeName?: string; // 提示框前缀
  afterName?: string; // 提示框后缀
}

const props = withDefaults(defineProps<Props>(), {
  isTenThousandUnits: true,
  colors: () => [
    "#368EF9",
    "#35C6FF",
    "#57FFD5",
    "#FDF94E",
    "#FFA05D",
    "#FF759E",
    "#D574FF",
    "#FF93F4"
  ],
  label: () => ({
    show: true,
    calculateField: "percentage"
  }),
  legend: () => ({
    show: true,
    position: "right",
    distance: 25,
    itemGap: 30,
    tipTypeName: ["数值", "占比"],
    nameWidth: 120
  }),
  beforeName: "",
  afterName: ""
});

const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value, null, { renderer: "svg" });
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.chartData.length) return;

  // 动态计算 edgeDistance，让标签尽可能靠近饼图外延
  const calculateEdgeDistance = (value: number, index: number) => {
    const totalValue = props.chartData.reduce((sum, item) => sum + item.value, 0);
    const percentage = value / totalValue;

    // 根据当前项的占比和位置动态调整距离
    // 占比越大，距离越近；占比越小，距离越远
    if (percentage >= 0.4) {
      // 占比很大的项，距离最近
      return "30%";
    } else if (percentage >= 0.2) {
      // 占比中等的项，距离适中
      return "25%";
    } else if (percentage >= 0.1) {
      // 占比较小的项，距离稍远
      return "15%";
    } else {
      // 占比很小的项，判断累计占比是否小于0.45
      const totalValue = props.chartData.reduce((sum, item) => sum + item.value, 0);
      let cumulative = 0;
      for (let i = 0; i <= index; i++) {
        cumulative += props.chartData[i].value / totalValue;
      }
      if (cumulative < 0.45) {
        return "35%";
      }
      return "10%";
    }
  };

  // 计算标签显示值
  const getLabelValue = (item: ChartDataItem) => {
    if (props.label.calculateField === "percentage") {
      return typeof item.percentage === "string"
        ? item.percentage
        : `${(item.percentage * 100).toFixed(0)}%`;
    } else {
      return props.isTenThousandUnits
        ? `${(item.value / 10000).toFixed(2)}`
        : item.value.toString();
    }
  };

  // 构建图例配置
  const buildLegendConfig = () => {
    if (!props.legend.show) return {};

    const legendConfig: any = {
      show: true,
      type: "scroll",
      icon: props.legend.icon || "circle",
      itemGap: props.legend.itemGap,
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
        rich: {
          name: {
            fontSize: 12,
            color: "#ffffff",
            width: props.legend.nameWidth || 120,
            align: "left"
          },
          percentage: {
            fontSize: 12,
            color: "#ffffff",
            align: "right"
          }
        }
      },
      formatter: (name: string) => {
        const value = props.chartData.find((item) => item.name === name)?.percentage;
        if (props.legend.isFormatterValue && typeof value === 'number') {
          return `{name|${name}}{percentage|${value.toFixed(0)}%}`;
        }
        return `{name|${name}}`;
      }
    };

    // 设置图例位置
    switch (props.legend.position) {
      case "top":
        legendConfig.top = 10;
        legendConfig.left = "center";
        break;
      case "bottom":
        legendConfig.bottom = 10;
        legendConfig.left = "center";
        break;
      case "left":
        legendConfig.left = 10;
        legendConfig.top = "center";
        legendConfig.orient = "vertical";
        break;
      case "right":
      default:
        legendConfig.right = props.legend.distance;
        legendConfig.top = "center";
        legendConfig.orient = "vertical";
        break;
    }

    return legendConfig;
  };

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: (params: any) => {
        const data = params.data;
        const labelValue = getLabelValue(data);
        return `<div style="display: flex; flex-direction: column; padding: 2px;">
          <div style="font-size: 12px; color: #fff;">${props.beforeName ? `${props.beforeName}：` : ""}${params.name}${props.afterName ? `${props.afterName}` : ""}</div>
          <div style="margin: 6px 0;font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 10px;">${props.tipTypeName?.[0]}</span>
            <span style="color: ${props.colors[params.dataIndex]}; font-size: 12px; font-weight: 600;">${labelValue}${props.isTenThousandUnits ? "万" : ""}</span>
          </div>
          <div style="font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 10px;">${props.tipTypeName?.[1]}</span>
            <span style="color: ${props.colors[params.dataIndex]}; font-size: 12px; font-weight: 600;">${data.percentage}%</span>
          </div>
        </div>`;
      },
      backgroundColor: "rgba(0, 0, 0, 0.9)",
      borderColor: "rgba(0, 0, 0, 0)",
      borderWidth: 1,
      borderRadius: 6,
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
        fontWeight: 500
      },
      padding: [6, 10]
    },
    grid: {
      top: "10%",
      left: "15%",
      right: "15%",
      bottom: "10%"
    },
    legend: buildLegendConfig(),
    series: [
      {
        name: "数据分布",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["35%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 12,
          borderColor: "transparent",
          borderWidth: 0
        },
        markPoint: {
          symbol: "rect"
        },
        labelLayout: {
          hideOverlap: false,
          moveOverlap: "shiftY"
        },
        labelLine: {
          show: false
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          percentage: item.percentage,
          label: {
            show: props.label.show,
            position: "outer",
            alignTo: "edge",
            edgeDistance: calculateEdgeDistance(item.value, index),
            formatter: props.label.formatter || `{dot|●} ${item.name}\n${getLabelValue(item)}`,
            rich: {
              dot: {
                color: props.colors[index % props.colors.length],
                fontSize: 8,
                padding: [0, 4, 0, 0]
              }
            },
            textStyle: {
              color: "#ffffff",
              fontSize: 12,
              fontWeight: 400
            }
          },
          itemStyle: {
            color: {
              type: "radial",
              x: 0,
              y: 1,
              r: 5,
              colorStops: [
                {
                  offset: 0,
                  color: props.colors[index % props.colors.length] // 中心色
                },
                {
                  offset: 0.5,
                  color: "rgba(0, 0, 0, 0.1)" // 渐变到中心色的透明度20%
                }
              ]
            }
          }
        }))
      }
    ]
  };

  chartInstance.setOption(option, true);
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听配置变化
watch(
  () => [props.colors, props.label, props.legend],
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="scss">
.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
