<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex justify-center items-center">
        <BubbleChart
          ref="chartRefForecast"
          :title="chartTitle"
          :subtitle="chartSubtitle"
          :data="chartData"
          :categories="categories"
          :colors="colors"
          width="90%"
          height="91%"
          theme="dark"
          :iconEnum="EventIconEnum"
        />
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import BubbleChart from "./components/bubble-chart.vue";
import { EventIconEnum } from "./components/icon-uri-enum";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/重要日期前中后对比.json";

const chartRefForecast = ref<any>(null);
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["passengerFlowForecast"]) {
    initChartData();
    nextTick(() => {
      chartRefForecast.value?.initChart();
    });
  } else {
    chartRefForecast.value?.getChartInstance()?.dispose();
  }
});

// 图表标题和副标题
const chartTitle = ref("客流趋势预测分析");
const chartSubtitle = ref("");

// 分类和颜色
const categories = ref(["活动前", "活动中", "活动后"]);
const colors = ref(["#FFFD89", "#3EECFF", "#B699FF"]);

// 数据状态
const jsonData = ref<any>(null);

// 初始化图表数据
const initChartData = () => {
  jsonData.value = chartDataJson.result;

  // 更新副标题
  if (chartDataJson.result) {
    // 使用第一个商业区的结论作为副标题
    chartSubtitle.value = chartDataJson.result.page_conclusion || "";
  }

  // 构建气泡图表数据
  buildBubbleChartData();
};

// 构建气泡图表数据
const buildBubbleChartData = () => {
  if (!jsonData.value || !jsonData.value.bds_list) {
    return;
  }

  const bubbleData: any[] = [];

  jsonData.value.bds_list.forEach((district: any) => {
    // 活动前数据
    bubbleData.push({
      name: district.bds_name,
      value: district.before_activity_flow,
      category: "活动前"
    });

    // 活动中数据
    bubbleData.push({
      name: district.bds_name,
      value: district.during_activity_flow,
      category: "活动中"
    });

    // 活动后数据
    bubbleData.push({
      name: district.bds_name,
      value: district.after_activity_flow,
      category: "活动后"
    });
  });

  chartData.value = bubbleData;
};

// 图表数据
const chartData = ref<any[]>([]);

const handleClick = () => {
  document.documentElement.requestFullscreen();
};
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}

.chart-container {
  width: 100%;
  height: calc(100% - 140px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
