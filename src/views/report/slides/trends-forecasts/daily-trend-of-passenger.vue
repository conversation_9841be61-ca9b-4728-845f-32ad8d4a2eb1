<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex justify-center">
        <LineChart
          ref="chartRefDay"
          :title="chartTitle"
          :subtitle="chartSubtitle"
          :x-axis-data="xAxisData"
          :series="chartSeries"
          width="90%"
          height="100%"
          theme="dark"
        >
          <template #chart-menu>
            <div class="w-full relative top-45 z-1000">
              <!-- 商业区标签导航 -->
              <BusinessDistrictTags
                :default-active="currentDistrict"
                @tag-change="handleTagChange"
              />
            </div>
          </template>
        </LineChart>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import LineChart from "./components/line-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/日客流分析.json";

const chartRefDay = ref<any>(null);
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["dailyTrendOfPassenger"]) {
    initChartData();
    nextTick(() => {
      chartRefDay.value?.initChart();
    });
  } else {
    chartRefDay.value?.getChartInstance()?.dispose();
  }
});
const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);
const chartData = ref<any>(null);

// 图表标题和副标题
const chartTitle = ref("客流分日走势");
const chartSubtitle = ref("");

// X轴数据（时间）- 从JSON数据中动态获取
const xAxisData = ref<string[]>([]);

// 初始化图表数据
const initChartData = () => {
  chartData.value = chartDataJson.result;

  // 更新副标题
  if (chartDataJson.result) {
    // 使用第一个商业区的结论作为副标题
    chartSubtitle.value = chartDataJson.result.page_conclusion || "";
  }

  // 初始化X轴数据
  if (
    chartDataJson.result &&
    chartDataJson.result.bds_list &&
    chartDataJson.result.bds_list.length > 0
  ) {
    const firstDistrict = chartDataJson.result.bds_list[0];
    if (firstDistrict.day_list) {
      xAxisData.value = firstDistrict.day_list.map((item: any) => {
        const date = new Date(item.day);
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        return `${month}.${day}`;
      });
    }
  }
};

// 根据商圈名称获取数据
const getDistrictData = (districtName: string) => {
  if (!chartData.value || !chartData.value.bds_list) {
    return null;
  }

  const district = chartData.value.bds_list.find((item: any) => item.bds_name === districtName);
  if (!district) {
    return null;
  }

  return {
    instant: district.day_list.map((item: any) => item.flow_day),
    average: Array(district.day_list.length).fill(district.average_flow),
    conclusion: district.bds_conclusion
  };
};

// 获取图表数据
const getChartData = (district: string) => {
  const districtData = getDistrictData(district);

  if (!districtData) {
    // 如果没有找到数据，返回默认数据
    return {
      instant: [],
      average: []
    };
  }

  return {
    instant: districtData.instant,
    average: districtData.average
  };
};

// 图表系列数据
const chartSeries = computed(() => {
  const data = getChartData(currentDistrict.value);
  return [
    {
      name: "当日客流",
      data: data.instant,
      color: "#C084FC",
      smooth: true,
      areaStyle: true,
      isShowSymbol: false,
      symbolIconType: "circle"
    },
    {
      name: "平均客流",
      data: data.average,
      color: "#22D3EE",
      smooth: false,
      areaStyle: true,
      isShowSymbol: false,
      symbolIconType: "none"
    }
  ];
});

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

const handleTagChange = (value: string) => {
  chartRefDay.value.getChartInstance().dispose();
  currentDistrict.value = value;
  updateChartData(value);
  chartRefDay.value.initChart();
};

const updateChartData = (district: string) => {
  // 更新X轴数据
  const districtData = getDistrictData(district);
  if (districtData) {
    // 重新计算X轴数据，确保与当前商业区的数据长度一致
    const districtInfo = chartData.value.bds_list.find((item: any) => item.bds_name === district);
    if (districtInfo && districtInfo.day_list) {
      xAxisData.value = districtInfo.day_list.map((item: any) => {
        const date = new Date(item.day);
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        return `${month}.${day}`;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}

.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
