<template>
  <layout-main>
    <div class="w-full h-full relative">
      <public-station-map
        ref="publicTransportationMapRef"
        :show-area="true"
        show-name
        :station-data="stationData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">公共交通枢纽分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ page_conclusion }}
              </p>
              <!-- 无数据兜底 -->
              <div v-if="!chartData.categories.length" class="no-data">暂无地铁站客流数据</div>
              <bar-chart v-else :data="chartData" />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import barChart from "./components/bar-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/商圈地铁客流分析.json";
import { useSlideStore } from "@/store/modules/slide";
import type { StationDataItem } from "@/views/report/hooks/useStationMarkerHook";
import { formatToWan } from "@/utils";

interface SubwayStation {
  station_name: string; // 地铁站名
  station_flow: number; // 客流量
  station_latitude: number; // 纬度
  station_longitude: number; // 经度
}

// 组件引用与状态
const publicTransportationMapRef = ref<any>(null);
const isShowDataPanel = ref(false);
const loaded = ref(false);
const slideStore = useSlideStore();
const chartTitle = ref("商圈周边地铁站的客流分布");

const { bds_list, page_conclusion } = chartDataJson.result;

const chartData = ref<{ categories: string[]; values: number[] }>({
  categories: [],
  values: []
});

const stationData = ref<StationDataItem[]>([]);

// 初始化数据：从JSON加载数据
const initData = () => {
  let stationList: SubwayStation[] = [];
  let mapData: StationDataItem[] = [];
  const categories: string[] = [];
  const values: number[] = [];

  bds_list.forEach((bds) => {
    bds.station_list.forEach((station) => {
      mapData.push({
        name: station?.station_name,
        position: {
          lat: station?.station_latitude || 0,
          lng: station?.station_longitude || 0
        },
        popupData: {
          station_name: station?.station_name || "", // 明确传递站点名
          station_flow: formatToWan(station?.station_flow || 0) // 客流数据（已转“万”单位）
        }
      });
      stationList.push(station);
    });
  });

  const stations = stationList.sort((a, b) => b.station_flow - a.station_flow);

  stations.forEach((station) => {
    categories.push(station.station_name);
    values.push(formatToWan(station.station_flow));
  });

  chartData.value = {
    categories,
    values
  };

  stationData.value = mapData;
};

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["publicTransportation"]) {
    initData();
    isShowDataPanel.value = true;
  } else {
    publicTransportationMapRef.value?.destroyMarkers();
    isShowDataPanel.value = false;
  }
});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("publicTransportation");
};

// 暴露组件方法
defineExpose({
  name: "publicTransportation",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("publicTransportation");
    } else {
      setTimeout(() => {
        publicTransportationMapRef.value?.initMap("商业周边地铁站客流分布");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 2000;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;

  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  // 无数据提示样式
  .no-data {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 60px 0;
    font-size: 14px;
  }
}

// 覆盖物悬停效果
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
