<template>
  <!-- 线图容器 -->
  <div class="line-chart-container">
    <!-- 图表内容区域 -->
    <div ref="chartRef" class="chart-content" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import * as echarts from "echarts";

// 图表数据项接口定义
interface ChartDataItem {
  name: string; // 数据系列名称
  data: number[]; // 数据数组
  color?: string; // 线条颜色（可选）
  smooth?: boolean; // 是否平滑曲线（可选）
  areaStyle?: boolean; // 是否显示区域填充（可选）
  isShowSymbol?: boolean; // 是否显示标记点（可选）
  symbolIconType?: string; // 标记点图标类型（可选）
}

// 组件属性接口定义
interface Props {
  xAxisData?: string[]; // X轴数据
  series?: ChartDataItem[]; // 数据系列
  option?: echarts.EChartsOption; // 自定义ECharts配置
  width?: string | number; // 图表宽度
  height?: string | number; // 图表高度
  theme?: "dark" | "light"; // 图表主题
  isTenThousandUnits?: boolean; // 是否显示万单位（可选）
}

// 定义组件属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  xAxisData: () => [],
  series: () => [],
  width: "100%",
  height: "400px",
  theme: "dark",
  isTenThousandUnits: true
});

// 图表DOM引用
const chartRef = ref<HTMLElement>();
// 图表实例
let chartInstance: echarts.ECharts | null = null;

// 计算属性：格式化后的系列数据
const formattedSeries = computed(() => {
  return props.series.map((item) => ({
    name: item.name,
    type: "line" as const,
    data: item.data,
    smooth: item.smooth ?? true,
    showSymbol: item.isShowSymbol ?? true,
    symbol: item.symbolIconType ?? "circle",
    symbolSize: 12,
    lineStyle: {
      width: 2,
      color: item.color || "#1FFFFF"
    },
    itemStyle: {
      color: item.color || "#1FFFFF"
    },
    areaStyle: item.areaStyle
      ? {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: (item.color || "#1FFFFF") + "40" },
            { offset: 1, color: (item.color || "#1FFFFF") + "10" }
          ])
        }
      : undefined
  }));
});

// 获取默认的图表配置
const getDefaultOption = (): echarts.EChartsOption => {
  return {
    backgroundColor: "transparent",
    title: { show: false },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(31, 26, 34, 0.9)",
      borderColor: "rgba(31, 26, 34, 0.9)",
      borderWidth: 1,
      textStyle: {
        color: "#fff",
        fontSize: 12
      },
      axisPointer: {
        type: "line",
        lineStyle: {
          color: "rgba(226, 246, 254, 1)",
          width: 1
        }
      },
      formatter: (params: any) => {
        if (!params || params.length === 0) return "";

        const chartHtml = `
          <div>
            <div style="font-size: 12px; font-weight: 400; color: #fff;">${params[0].name}</div>
            ${params
              .map((param: any, index: number) => {
                const color = param.color || (index === 0 ? "#A967FF" : "#1FFFFF");
                const value = props.isTenThousandUnits
                  ? (param.value / 10000).toFixed(2) + "万"
                  : param.value.toString();

                return `
                <div style="display: flex; justify-content: space-between; align-items: center; gap: 10px;">
                  <div style="width: 20px; height: 4px; background-color: ${color};"></div>
                  <div style="font-size: 12px; font-weight: 400; color: #fff;">${param.seriesName}</div>
                  <div style="font-size: 12px; font-weight: 400; color: ${color};">${value}</div>
                </div>
              `;
              })
              .join("")}
          </div>
        `;
        return chartHtml;
      }
    },
    legend: {
      top: 10,
      right: 20,
      textStyle: {
        color: "#fff",
        fontSize: 14
      },
      itemWidth: 12,
      itemHeight: 12
    },
    grid: {
      top: 60,
      left: 0,
      right: 10,
      bottom: 30,
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: props.xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8490A9"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 12
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8490A9"
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 12,
        formatter: (value: number) => {
          if (props.isTenThousandUnits) {
            return value > 0 ? (value / 10000).toFixed(1) + "万" : "0";
          }
          return value.toString();
        }
      }
    },
    series: formattedSeries.value
  };
};

// 初始化图表
const initChart = (): void => {
  try {
    if (!chartRef.value) {
      console.warn("图表容器未找到");
      return;
    }

    // 销毁已存在的实例
    if (chartInstance) {
      chartInstance.dispose();
    }

    // 创建ECharts实例
    chartInstance = echarts.init(chartRef.value, props.theme, { 
      renderer: "svg",
      useDirtyRect: true // 启用脏矩形优化
    });

    // 合并用户配置和默认配置
    const finalOption = props.option
      ? echarts.util.merge(getDefaultOption(), props.option)
      : getDefaultOption();

    // 设置图表配置
    chartInstance.setOption(finalOption, true); // 第二个参数为true表示不合并配置

    // 监听窗口大小变化
    window.addEventListener("resize", handleResize);
  } catch (error) {
    console.error("初始化图表失败:", error);
  }
};

// 更新图表
const updateChart = (): void => {
  try {
    if (!chartInstance) {
      console.warn("图表实例未初始化");
      return;
    }

    // 重新合并配置
    const finalOption = props.option
      ? echarts.util.merge(getDefaultOption(), props.option)
      : getDefaultOption();

    // 更新图表配置
    chartInstance.setOption(finalOption, true);
  } catch (error) {
    console.error("更新图表失败:", error);
  }
};

// 处理窗口大小变化
const handleResize = (): void => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 监听数据变化，自动更新图表
watch(
  () => [props.series, props.xAxisData, props.option],
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener("resize", handleResize);
  
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 暴露方法给父组件使用
defineExpose({
  // 获取图表实例
  getChartInstance: () => chartInstance,
  // 重新调整图表大小
  resize: () => chartInstance?.resize(),
  // 设置图表配置
  setOption: (option: echarts.EChartsOption) => {
    if (chartInstance) {
      chartInstance.setOption(option, true);
    }
  },
  // 初始化图表
  initChart: () => initChart(),
  // 销毁图表
  dispose: () => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  }
});
</script>

<style lang="scss" scoped>
.line-chart-container {
  width: v-bind(width);
  height: v-bind(height);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .chart-content {
    width: 100%;
    height: 100%;
  }
}

.tooltip-box {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .itme-type {
    color: #fff;
    font-size: 12px;
    font-weight: 400;
  }

  .item-data {
    display: flex;
    align-items: center;
    gap: 10px;

    /* 提示框图标 */
    .item-icon {
      width: 20px;
      height: 5px;
    }
    /* 紫色图标 */
    .purple {
      background-color: #a967ff;
    }
    /* 绿色图标 */
    .green {
      background-color: #1fffff;
    }

    /* 提示框项目名称 */
    .tooltip-item-name {
      color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
    /* 提示框项目数值 */
    .tooltip-item-value {
      color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
  }
}
</style>
