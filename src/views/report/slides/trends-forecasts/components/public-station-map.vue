<template>
  <div ref="publicTransportationMap" class="c-common-map w-full h-full"></div>
</template>

<script setup lang="ts">
import districtHomeData from "@/data/business-district-overview/商圈首页.json";
import { useBdsTitleHook } from "@/views/report/hooks/useBdsTitleHook";
import { useBdsAreaHook } from "@/views/report/hooks/useBdsAreaHook";
import {
  useStationMarkerHook,
  type StationDataItem
} from "@/views/report/hooks/useStationMarkerHook";
import { BdsAreaData } from "@/views/report/type";

const { createMarkers, destroyMarkers } = useStationMarkerHook();

const publicTransportationMap: any = ref(null);
const loaded = ref(false); // 地图是否加载完成
const isLoading = ref(false); // 地图是否正在加载
const TMap = (window as any).TMap; // 地图类
let mapInstance: any = ref(null);

const emit = defineEmits(["load"]);

const props = defineProps<{
  stationData: StationDataItem[];
}>();

const initMap = async (name: string) => {
  if (loaded.value || isLoading.value) return;
  try {
    console.log(name, "地图加载中...");
    isLoading.value = true;
    // 加载地图实例
    const map = await new TMap.Map(publicTransportationMap.value, {
      zoom: 14.5, // 设置地图缩放级别
      viewMode: "3D", // 设置地图模式
      pitch: 45, // 设置俯仰角
      rotation: -20, // 设置地图旋转角度
      mapStyleId: "style1",
      zIndex: 10,
      minZoom: 13,
      maxZoom: 18,
      center: new TMap.LatLng(31.31331, 120.69778), // 设置中心点
      baseMap: {
        type: "vector",
        features: ["base", "building3d", "label"],
        buildingRange: [14.5, 23] // 设置建筑物楼块的显示级别，目前设置成了所支持的最大范围[14.5, 25]
      },
      renderOptions: {
        fogOptions: {
          color: "rgba(11, 27, 36, 0.1)"
        }
      }
    });
    if (map) {
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM); // 从地图容器移出 缩放控件,
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE); // 从地图容器移出 比例尺控件,
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION); // 从地图容器移出 旋转控件,
    }
    map.on("tilesloaded", async () => {
      if (!loaded.value) {
        loaded.value = true;
        emit("load", true);

        const { bds_list: bdsMapData = [] } = districtHomeData.result;
        // 绘制商圈围栏
        createMultiPolygon(bdsMapData, map);
        // 绘制商圈名称
        createBdsTitle(bdsMapData, map);

        updateMapData(map);
      }
    });

    mapInstance.value = map;
  } catch (error) {
    console.error("地图加载失败:", error);
  } finally {
    isLoading.value = false;
  }
};

const updateMapData = (mapInstance: any) => {
  if (props.stationData.length === 0) return;
  console.log("触发渲染111");
  destroyMarkers();
  //   // 绘制地铁数据弹窗
  createMarkers(props.stationData, mapInstance);
};

// 创建商圈围栏
const createMultiPolygon = (data: BdsAreaData[], mapInstance: any) => {
  const areaData = data.map((item: BdsAreaData) => {
    return {
      bds_id: item.bds_id,
      bds_name: item.bds_name
    };
  });
  useBdsAreaHook(mapInstance, areaData, { clickable: false, sortable: false });
};

// 创建商圈名称
const createBdsTitle = (data: any, mapInstance: any) => {
  useBdsTitleHook(mapInstance, data, { sortable: false });
};

watch(
  () => props.stationData,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      console.log("触发渲染222");
      updateMapData(mapInstance.value);
    }
  }
);

onUnmounted(() => {
  mapInstance.value && mapInstance.value?.destroy();
});

defineExpose({
  initMap,
  destroyMarkers
});
</script>

<style lang="scss"></style>
