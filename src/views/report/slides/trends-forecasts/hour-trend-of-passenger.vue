<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex justify-center">
        <LineChart
          ref="chartRefHour"
          :title="chartTitle"
          :subtitle="chartSubtitle"
          :x-axis-data="xAxisData"
          :series="chartSeries"
          :is-ten-thousand-units="true"
          width="90%"
          height="100%"
          theme="dark"
        >
          <template #chart-menu>
            <div class="w-full relative top-45 z-1000">
              <!-- 商业区标签导航 -->
              <BusinessDistrictTags
                :default-active="currentDistrict"
                @tag-change="handleTagChange"
              />
            </div>
          </template>
        </LineChart>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import LineChart from "./components/line-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/平均客流分时走势.json";

const chartRefHour = ref<any>(null);
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["hourTrendOfPassenger"]) {
    initChartData();
    nextTick(() => {
      chartRefHour.value?.initChart();
    });
  } else {
    chartRefHour.value?.getChartInstance()?.dispose();
  }
});

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);
const chartData = ref<any>(null);

// 图表标题和副标题
const chartTitle = ref("平均客流分时走势");
const chartSubtitle = ref("");

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// X轴数据（时间）- 从JSON数据中动态获取
const xAxisData = ref<string[]>([]);

// 根据商圈名称获取数据
const getDistrictData = (districtName: string) => {
  return processedDistrictData.value[districtName] || null;
};

// 更新图表X轴数据
const updateChartXAxisData = (district: string) => {
  const districtData = processedDistrictData.value[district];
  if (districtData) {
    xAxisData.value = districtData.xAxisData;
  }
};

// 获取图表数据
const getChartData = (district: string) => {
  const districtData = processedDistrictData.value[district];

  if (!districtData) {
    // 如果没有找到数据，返回默认数据
    return {
      instant: [],
      average: []
    };
  }

  return {
    instant: districtData.instant,
    average: districtData.average
  };
};

// 图表系列数据
const chartSeries = computed(() => {
  const data = getChartData(currentDistrict.value);
  return [
    {
      name: "时刻客流",
      data: data.instant,
      color: "#C084FC",
      smooth: true,
      areaStyle: true,
      isShowSymbol: false,
      symbolIconType: "circle"
    },
    {
      name: "平均客流",
      data: data.average,
      color: "#22D3EE",
      smooth: false,
      areaStyle: true,
      isShowSymbol: false,
      symbolIconType: "none"
    }
  ];
});

// 初始化图表数据
const initChartData = () => {
  chartData.value = chartDataJson.result; // 注意这里使用 data.result

  // 更新副标题
  if (chartDataJson.result) {
    // 使用第一个商业区的结论作为副标题
    chartSubtitle.value = chartDataJson.result?.page_conclusion || "";
  }

  // 预处理所有商圈的数据
  if (
    chartDataJson.result &&
    chartDataJson.result.bds_list &&
    chartDataJson.result.bds_list.length > 0
  ) {
    chartDataJson.result.bds_list.forEach((district: any) => {
      if (district.hour_list) {
        const timeRangeList = district.hour_list;

        // 生成图表数据
        const instant = timeRangeList.map((item: any) => item.flow_h);
        const average = Array(instant.length).fill(district.average_flow);

        // 生成X轴数据
        const xAxisData = timeRangeList.map((item: any) => {
          const hour = item.hour.toString().padStart(2, "0");
          return `${hour}:00`;
        });

        // 存储预处理的数据
        processedDistrictData.value[district.bds_name] = {
          xAxisData,
          instant,
          average
        };
      }
    });

    // 初始化第一个商圈的数据
    const firstDistrict =
      chartDataJson.result.bds_list.find(
        (district: any) => district.bds_name === currentDistrict.value
      ) || chartDataJson.result.bds_list[0];
    if (firstDistrict) {
      currentDistrict.value = firstDistrict.bds_name;
      updateChartXAxisData(firstDistrict.bds_name);
    }
  }
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

const handleTagChange = (value: string) => {
  chartRefHour.value.getChartInstance().dispose();
  currentDistrict.value = value;
  updateChartXAxisData(value);
  chartRefHour.value.initChart();
};
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}

.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
