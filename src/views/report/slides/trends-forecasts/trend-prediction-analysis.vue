<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-box w-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-justify-between items-center">
            <div 
              v-for="chartConfig in chartConfigs" 
              :key="chartConfig.key"
              class="chart-item"
            >
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ chartConfig.title }}</span>
              </div>
              <div class="chart-line">
                <LineChartSmall
                  v-if="isShowChart"
                  :x-axis-data="chartConfig.xAxisData"
                  :series="chartConfig.data"
                  width="420px"
                  height="370px"
                  theme="dark"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import LineChartSmall from "./components/line-chart-small.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/重要日期.json";

// 类型定义
interface ChartData {
  name: string;
  smooth: boolean;
  areaStyle: boolean;
  isShowSymbol: boolean;
  symbolIconType: string;
  data: number[];
  color: string;
}

interface ProcessedDistrictData {
  qingMingXAxisData: string[];
  qingMingData: ChartData;
  mayDayXAxisData: string[];
  mayDayData: ChartData;
  dragonBoatXAxisData: string[];
  dragonBoatData: ChartData;
}

interface ChartConfig {
  key: string;
  title: string;
  data: ChartData;
  xAxisData: string[];
}

// 常量配置
const HOLIDAY_DATES = {
  QING_MING: ["2025-04-04", "2025-04-05", "2025-04-06"],
  MAY_DAY: ["2025-05-01", "2025-05-02", "2025-05-03", "2025-05-04", "2025-05-05"],
  DRAGON_BOAT: ["2025-05-31", "2025-06-01", "2025-06-02"]
} as const;

const CHART_COLORS = {
  QING_MING: "#19FFDC",
  MAY_DAY: "#FFA0E9", 
  DRAGON_BOAT: "#F8FF6B"
} as const;

const SERIES_CONFIG = {
  name: "当日客流",
  smooth: false,
  areaStyle: true,
  isShowSymbol: true,
  symbolIconType: "circle"
} as const;

// 响应式数据
const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);
const chartTitle = ref("客流趋势预测分析");
const chartTitleDesc = ref("");

// 图表数据
const qingMingFestivalData = ref<ChartData>({ ...SERIES_CONFIG, data: [], color: CHART_COLORS.QING_MING });
const qingMingFestivalXAxisData = ref<string[]>([]);
const mayDayHolidayData = ref<ChartData>({ ...SERIES_CONFIG, data: [], color: CHART_COLORS.MAY_DAY });
const mayDayHolidayXAxisData = ref<string[]>([]);
const dragonBoatFestivalData = ref<ChartData>({ ...SERIES_CONFIG, data: [], color: CHART_COLORS.DRAGON_BOAT });
const dragonBoatFestivalXAxisData = ref<string[]>([]);

// 预处理的商圈数据
const processedDistrictData = ref<Record<string, ProcessedDistrictData>>({});

// 计算属性：图表配置
const chartConfigs = computed<ChartConfig[]>(() => [
  {
    key: 'qingMing',
    title: '清明节每日客流',
    data: [qingMingFestivalData.value],
    xAxisData: qingMingFestivalXAxisData.value
  },
  {
    key: 'mayDay',
    title: '五一节每日客流',
    data: [mayDayHolidayData.value],
    xAxisData: mayDayHolidayXAxisData.value
  },
  {
    key: 'dragonBoat',
    title: '端午节每日客流',
    data: [dragonBoatFestivalData.value],
    xAxisData: dragonBoatFestivalXAxisData.value
  }
]);

// 工具函数：日期格式化
const formatDate = (date: string): string => {
  const dateObj = new Date(date);
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  return `${month}月${day}日`;
};

// 工具函数：处理节假日数据
const processHolidayData = (dayList: any[], holidayDates: string[]): { data: number[], xAxisData: string[] } => {
  const data: number[] = [];
  const xAxisData: string[] = [];
  
  dayList.forEach((item: any) => {
    if (holidayDates.includes(item.day)) {
      data.push(item.flow_day);
      xAxisData.push(formatDate(item.day));
    }
  });
  
  return { data, xAxisData };
};

const isShowChart = ref(false);
// 更新图表数据
const updateChartData = (): void => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (!currentData) return;
  isShowChart.value = false;
  nextTick(() => {
    // 更新清明节数据
    qingMingFestivalData.value.data = currentData.qingMingData.data;
    qingMingFestivalXAxisData.value = currentData.qingMingXAxisData;
    
    // 更新五一节数据
    mayDayHolidayData.value.data = currentData.mayDayData.data;
    mayDayHolidayXAxisData.value = currentData.mayDayXAxisData;
    
    // 更新端午节数据
    dragonBoatFestivalData.value.data = currentData.dragonBoatData.data;
    dragonBoatFestivalXAxisData.value = currentData.dragonBoatXAxisData;

    isShowChart.value = true;
  });
};

// 清理图表数据
const clearChartData = (): void => {
  qingMingFestivalData.value.data = [];
  mayDayHolidayData.value.data = [];
  dragonBoatFestivalData.value.data = [];
  qingMingFestivalXAxisData.value = [];
  mayDayHolidayXAxisData.value = [];
  dragonBoatFestivalXAxisData.value = [];
};

// 初始化数据
const initData = (): void => {
  try {
    const { bds_list, page_conclusion } = chartDataJson.result;
    chartTitleDesc.value = page_conclusion;

    // 预处理所有商圈的数据
    bds_list.forEach((bds: any) => {
      const districtName = bds.bds_name;
      
      // 处理各节假日数据
      const qingMingData = processHolidayData(bds.day_list, HOLIDAY_DATES.QING_MING);
      const mayDayData = processHolidayData(bds.day_list, HOLIDAY_DATES.MAY_DAY);
      const dragonBoatData = processHolidayData(bds.day_list, HOLIDAY_DATES.DRAGON_BOAT);

      // 存储处理后的数据
      processedDistrictData.value[districtName] = {
        qingMingXAxisData: qingMingData.xAxisData,
        qingMingData: {
          ...SERIES_CONFIG,
          data: qingMingData.data,
          color: CHART_COLORS.QING_MING
        },
        mayDayXAxisData: mayDayData.xAxisData,
        mayDayData: {
          ...SERIES_CONFIG,
          data: mayDayData.data,
          color: CHART_COLORS.MAY_DAY
        },
        dragonBoatXAxisData: dragonBoatData.xAxisData,
        dragonBoatData: {
          ...SERIES_CONFIG,
          data: dragonBoatData.data,
          color: CHART_COLORS.DRAGON_BOAT
        }
      };
    });
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
};

// 处理标签切换
const handleTagChange = (tag: string): void => {
  if (tag === currentDistrict.value) return;
  
  clearChartData();
  currentDistrict.value = tag;
  
  nextTick(() => {
    updateChartData();
  });
};

// 处理双击事件
const handleClick = (): void => {
  document.documentElement.requestFullscreen();
};

// 使用幻灯片钩子
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["trendPredictionAnalysis"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
  } else {
    clearChartData();
  }
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    line-height: 18px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 5%;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0 4px 6px #4d429e;
        }
      }

      .chart-line {
        width: 420px;
        height: 370px;
      }
    }
  }
}
</style>
