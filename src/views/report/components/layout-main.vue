<template>
  <div class="v-layout-main w-full h-full relative" :class="{ 'common-bg': showBg }">
    <layout-header
      :data-intro="header?.dataIntro"
      :data-desc="header?.dataDesc"
      :show-tips="header?.showTips"
    ></layout-header>
    <div class="v-layout-main__content w-full h-full"><slot></slot></div>
    <layout-footer></layout-footer>
  </div>
</template>

<script setup lang="ts">
import type { HeaderType } from "./layout-header.vue";
interface LayoutMainType {
  header?: HeaderType;
  showBg?: boolean;
}

withDefaults(defineProps<LayoutMainType>(), {
  showBg: true
});
</script>

<style scoped>
.v-layout-main {
  &.common-bg {
    background: url("@/assets/visitor-group-bg.png") no-repeat;
    background-size: 100% 100%;
    background-position: center;
  }
}
</style>
