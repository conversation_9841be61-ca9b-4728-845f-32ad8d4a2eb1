<template>
  <div class="concentric-pie-chart" ref="chartRef" :style="chartStyle"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

// 定义数据接口
interface PieData {
  name: string;
  value: number;
  color?: string;
}

// 定义组件配置接口
interface ChartConfig {
  width?: string | number;
  height?: string | number;
  title?: string;
  colors?: string[];
  radius?: [string, string];
  center?: [string, string];
  showLegend?: boolean;
  legendPosition?: "top" | "bottom" | "left" | "right";
  legendOrient?: "horizontal" | "vertical";
  showTooltip?: boolean;
  showLabel?: boolean;
  labelPosition?: "inside" | "outside" | "center";
  padAngle?: number;
  borderRadius?: number;
  emphasisScale?: boolean;
  emphasisShadowBlur?: number;
  legendItemGap?: number;
  startAngle?: number;
  legendDistance?: number;
  tipTypeName?: string[];
  isTenThousandUnits?: boolean;
  roseType?: boolean | "radius" | "area";
  legendNameWidth?: number;
}

// 定义 props
const props = withDefaults(
  defineProps<{
    data: PieData[];
    config?: ChartConfig;
  }>(),
  {
    config: () => ({})
  }
);

// 合并配置：父组件配置 + 默认配置
const mergedConfig = computed<ChartConfig>(() => {
  const defaultConfig: ChartConfig = {
    width: 420,
    height: 370,
    colors: ["#488AFE", "#8B5CF6", "#F59E0B", "#10B981", "#EF4444", "#06B6D4"],
    radius: ["20%", "55%"],
    center: ["32%", "50%"],
    showLegend: true,
    legendPosition: "right",
    legendOrient: "vertical",
    showTooltip: true,
    showLabel: false,
    labelPosition: "center",
    padAngle: 5,
    borderRadius: 10,
    emphasisScale: true,
    emphasisShadowBlur: 10,
    legendItemGap: 15,
    startAngle: 90,
    legendDistance: 10,
    tipTypeName: ["数量", "占比"],
    isTenThousandUnits: true,
    roseType: false,
    legendNameWidth: 120
  };

  // 深度合并配置
  return {
    ...defaultConfig,
    ...props.config
  };
});

// 响应式引用
const chartRef = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

// 计算图表样式
const chartStyle = computed(() => {
  const { width, height } = mergedConfig.value;

  return {
    width: typeof width === "number" ? `${width}px` : width,
    height: typeof height === "number" ? `${height}px` : height
  };
});

// 计算图表数据
const chartData = computed<PieData[]>(() => {
  return props.data && props.data.length > 0 ? props.data : [];
});

// 计算总数
const total = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0);
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return;

  if (myChart) {
    myChart.dispose();
  }

  myChart = echarts.init(chartRef.value, null, {
    renderer: "svg"
  });

  const option = getChartOption();
  myChart.setOption(option);
};

// 获取图表配置
const getChartOption = () => {
  const {
    colors,
    radius,
    center,
    showLegend,
    legendPosition,
    legendOrient,
    showTooltip,
    showLabel,
    labelPosition,
    padAngle,
    borderRadius,
    emphasisScale,
    emphasisShadowBlur,
    legendItemGap,
    startAngle,
    legendDistance,
    tipTypeName,
    isTenThousandUnits,
    roseType,
    legendNameWidth
  } = mergedConfig.value;

  // 计算图例位置
  const legendPositionMap = {
    top: { top: legendDistance, left: "center" },
    bottom: { bottom: legendDistance, left: "center" },
    left: { left: legendDistance, top: "center" },
    right: { right: legendDistance, top: "center" }
  };

  const option = {
    tooltip: showTooltip
      ? {
          trigger: "item",
          formatter: (params: any) => {
            const formatValue = isTenThousandUnits
              ? (params.value / 10000).toFixed(2)
              : params.value;
            return `<div class="fs-12">
              <div class="fw-700 c-fff">${params.name}${params.data.extraName ? `${params.data.extraName}` : ""}</div>
              <div class="mtb-6 flex justify-between items-center">
                <span  class="c-e2f6fe pr-10">${tipTypeName?.[0]}</span>
                <span style="color: ${params.color};">${formatValue}${isTenThousandUnits ? "万" : ""}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="c-e2f6fe pr-10">${tipTypeName?.[1]}</span>
                <span style="color: ${params.color};">${params.percent}%</span>
              </div>
            </div>`;
          },
          backgroundColor: "#00305D",
          borderColor: "#00305D",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            lineHeight: 24
          }
        }
      : undefined,

    legend: showLegend
      ? {
          orient: legendOrient,
          ...legendPositionMap[legendPosition as keyof typeof legendPositionMap],
          icon: "circle",
          itemWidth: 12,
          itemHeight: 12,
          itemGap: legendItemGap,
          formatter: (name: string) => {
            const item = chartData.value.find((item) => item.name === name);
            if (!item) return name;
            const percentage =
              total.value > 0 ? ((item.value / total.value) * 100).toFixed(0) : "0";
            return `{name|${name}}{percentage|${percentage}%}`;
          },
          // 富文本样式配置
          textStyle: {
            fontSize: 14,
            color: "#fff",
            rich: {
              name: {
                fontSize: 14,
                color: "#fff",
                width: legendNameWidth, // 使用配置的名称区域宽度
                align: "left" // 名称左对齐
              },
              percentage: {
                fontSize: 14,
                color: "#fff",
                align: "right" // 百分比右对齐
              }
            }
          }
        }
      : undefined,

    roseType: roseType,

    series: [
      {
        name: "数据分布",
        type: "pie",
        radius: radius,
        center: center,
        data: chartData.value,
        avoidLabelOverlap: false,
        padAngle: padAngle,
        startAngle: startAngle,
        itemStyle: {
          borderRadius: borderRadius
        },
        minAngle: 5,
        label: showLabel
          ? {
              show: true,
              position: labelPosition,
              formatter: (params: any) => {
                if (labelPosition === "center") {
                  return `{name|${params.name}}\n{value|${params.percent}%}`;
                }
                return `${params.name}\n${params.percent}%`;
              },
              rich:
                labelPosition === "center"
                  ? {
                      name: {
                        fontSize: 14,
                        color: "#fff",
                        lineHeight: 20
                      },
                      value: {
                        fontSize: 20,
                        fontWeight: "bold",
                        color: "#fff",
                        lineHeight: 24
                      }
                    }
                  : undefined
            }
          : {
              show: false
            },

        emphasis: {
          label: showLabel
            ? {
                show: true,
                fontSize: 16,
                fontWeight: "bold"
              }
            : undefined,
          itemStyle: {
            shadowBlur: emphasisShadowBlur,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            scale: emphasisScale ? 1.1 : 1
          }
        }
      }
    ]
  };

  // 设置自定义颜色
  if (colors && colors.length > 0) {
    option.color = colors;
  }

  return option;
};

// 更新图表
const updateChart = () => {
  if (myChart && chartData.value.length > 0) {
    const option = getChartOption();
    myChart.setOption(option, true);
  }
};

// 监听数据变化
watch(
  chartData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听配置变化
watch(
  () => mergedConfig.value,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});

// 暴露方法给父组件
defineExpose({
  updateChart
});
</script>

<style scoped>
.concentric-pie-chart {
  position: relative;
}
</style>
