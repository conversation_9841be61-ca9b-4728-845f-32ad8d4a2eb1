<template>
  <div
    class="card-container"
    :class="cardType"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  cardType?: "cyan" | "purple"; // 控制背景图片类型 cyan 青色边框，purple 紫色边框
  width?: number;
  height?: number;
}

withDefaults(defineProps<Props>(), {
  cardType: "cyan",
  width: 470,
  height: 465
});
</script>

<style scoped lang="scss">
.card-container {
  box-sizing: border-box;
  position: relative;
  z-index: 11;
  min-height: 200px;
  pointer-events: none;
  backdrop-filter: blur(5px);
  border-radius: 12px;
  &.cyan {
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;
      background-image:
        linear-gradient(to right, #1d3452, #1d3452),
        linear-gradient(45deg, transparent, #9efff0, transparent);
      border-radius: 12px;
      opacity: 0.65;
      z-index: -1;
    }
  }

  &.purple {
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;
      background-image:
        linear-gradient(to right, #1d3452, #1d3452),
        linear-gradient(45deg, transparent, #a56dff, transparent);
      border-radius: 12px;
      opacity: 0.65;
      z-index: -1;
    }
  }

  .card-content {
    height: 100%;
    position: relative;
    z-index: 1;
    pointer-events: auto;
  }
}
</style>
