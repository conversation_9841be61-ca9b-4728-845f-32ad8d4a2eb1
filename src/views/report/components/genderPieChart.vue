<template>
  <div class="age-radar-chart" ref="chartRef"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

interface AgeData {
  name: string;
  value: number;
}

// 定义 props 接收父组件传递的数据
const props = defineProps<{
  data: AgeData[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

// 使用计算属性确保数据响应式更新
const chartData = computed<AgeData[]>(() => {
  return props.data && props.data.length > 0
    ? props.data
    : [
        { name: "男", value: 0 },
        { name: "女", value: 0 }
      ];
});

const initChart = () => {
  if (chartRef.value) {
    if (myChart) {
      myChart.dispose();
    }

    myChart = echarts.init(chartRef.value);

    const total = chartData.value.reduce((sum, item) => sum + item.value, 0);

    const option = {
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)",
        backgroundColor: "rgba(0,0,0,0.7)",
        borderColor: "#488AFE",
        borderWidth: 1,
        borderRadius: 8,
        textStyle: {
          color: "#fff"
        }
      },
      legend: {
        orient: "vertical",
        itemGap: 15,
        right: 35,
        top: 160,
        icon: "circle",
        textStyle: {
          fontSize: 12,
          color: "#fff"
        },
        formatter: (name: string) => {
          const tarValue = chartData.value.find((item) => item.name === name)?.value || 0;
          const percentage = total > 0 ? ((tarValue / total) * 100).toFixed(0) : "0";
          return `${name}  ${percentage}%`;
        }
      },
      series: [
        {
          name: "性别分布",
          type: "pie",
          radius: ["40%", "70%"],
          center: ["39%", "50%"],

          avoidLabelOverlap: false,
          padAngle: 5,
          itemStyle: {
            borderRadius: 10
          },
          label: {
            show: false,
            position: "center"
          },
          emphasis: {
            label: {
              show: false,
              fontSize: 20,
              fontWeight: "bold",
              formatter: (params: any) => {
                return `{name|${params.name}}\n{value|${params.percent}%}`;
              },
              rich: {
                name: {
                  fontSize: 16,
                  color: "#fff",
                  lineHeight: 24
                },
                value: {
                  fontSize: 24,
                  color: "#fff",
                  fontWeight: "bold",
                  lineHeight: 30
                }
              }
            }
          },
          labelLine: {
            show: false
          },
          data: chartData.value
        }
      ],
      color: ["#BB7AF9", "#3B82F6"]
    };

    myChart.setOption(option);
  }
};

// 监听数据变化，重新渲染图表
watch(
  () => props.data,
  () => {
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    // initChart();
  });
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
});
// 暴露方法给父组件使用
defineExpose({
  // 初始化图表
  initChart: () => initChart(),
  myChart: () => myChart
});
</script>

<style scoped>
.age-radar-chart {
  /* width: 363px;
  height: 300px; */
  width: 463px;
  height: 400px;
}
</style>
