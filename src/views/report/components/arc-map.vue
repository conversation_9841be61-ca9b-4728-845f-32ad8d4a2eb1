<template>
  <div class="w-full h-full z-1 relative">
    <div ref="arcMap" class="c-common-map"></div>
  </div>
</template>

<script setup lang="ts">
// 基础接口定义
interface MapCenter {
  lat: number;
  lng: number;
}

export interface ArcDataItem {
  from: { lat: number; lng: number };
  to: { lat: number; lng: number };
  index?: number;
}

interface MapProps {
  center?: MapCenter;
  mapOptions?: Record<string, any>;
  arcData?: ArcDataItem[];
  flyDirection?: "fromTo" | "toFrom";
  animationSpeed?: number;
}

// 接收Props（默认值不变）
const props = withDefaults(defineProps<MapProps>(), {
  center: () => ({ lat: 33.520718, lng: 121.900692 }),
  mapOptions: () => ({}),
  arcData: () => [],
  flyDirection: "fromTo",
  animationSpeed: 5
});

// 组件状态：新增地图实例是否已初始化的标记
const arcMap = ref<HTMLElement | null>(null);
const isLoading = ref(false);
const loaded = ref(false); // 标记地图实例是否已创建
const TMap = (window as any).TMap;
const arcLayer = ref<any>(null);
const mapInstance = ref(null);

const emit = defineEmits(["load"]);

// 1. 处理飞线数据（方向+索引，逻辑不变）
const formatArcData = () => {
  return props.arcData.map((item, originalIndex) => {
    const directionProcessed =
      props.flyDirection === "toFrom" ? { from: item.to, to: item.from } : { ...item };
    return { ...directionProcessed, index: originalIndex };
  });
};

// 2. 飞线颜色配置（逻辑不变）
const getArcColors = (index: number) => {
  const colorConfig = [
    { staticColor: "rgba(255, 144, 178, 0.3)", animateColor: "rgba(255, 144, 178, 1)" },
    { staticColor: "rgba(255, 233, 149, 0.3)", animateColor: "rgba(255, 233, 149, 1)" },
    { staticColor: "rgba(164, 254, 255, 0.3)", animateColor: "rgba(164, 254, 255, 1)" },
    { staticColor: "rgba(195, 227, 255, 0.3)", animateColor: "rgba(195, 227, 255, 1)" }
  ];
  return index < 3 ? colorConfig[index] : colorConfig[3];
};

// 3. 飞线样式（逻辑不变）
const pickStyle = (arcLine: ArcDataItem) => {
  const arcIndex = arcLine.index ?? 0;
  const { staticColor, animateColor } = getArcColors(arcIndex);
  return {
    color: staticColor,
    animateColor: animateColor,
    width: 4,
    animate: true,
    animateSpeed: props.animationSpeed,
    animateInterval: 100,
    dashArray: "15, 8",
    animateLength: 30
  };
};

// 4. 核心优化：仅销毁飞线图层（保留地图实例）
const destroyArcLayer = async () => {
  if (arcLayer.value) {
    await arcLayer.value.updateAnimation("process", { enable: false });
    arcLayer.value.destroy(); // 移除飞线图层（不销毁地图）
    arcLayer.value = null;
    console.log("飞线图层已销毁");
  }
};

// 初始化飞线
const initOrUpdateArcLayer = async () => {
  if (!mapInstance.value || isLoading.value || !loaded.value) return;

  // 先销毁旧飞线，避免叠加
  await destroyArcLayer();

  // 重建飞线图层
  arcLayer.value = await new TMap.visualization.Arc({
    mode: "vertical",
    pickStyle: pickStyle
  }).addTo(mapInstance.value);

  startAnimation();
  console.log("飞线图层已创建");
};

// 6. 地图初始化：仅执行一次（页面切换不重复创建）
const initMap = async (name: string) => {
  if (!arcMap.value || loaded.value || isLoading.value) return;

  try {
    isLoading.value = true;
    console.log(`${name} 地图初始化中...`);
    // 创建地图实例（仅第一次执行）
    const map = new TMap.Map(arcMap.value, {
      zoom: 8,
      viewMode: "2D",
      mapStyleId: "style1",
      zIndex: 10,
      maxZoom: 14,
      center: new TMap.LatLng(props.center.lat, props.center.lng),
      ...props.mapOptions,
      renderOptions: {
        fogOptions: {
          color: "rgba(11, 27, 36, 0.1)"
        }
      }
    });
    // 移除默认控件（仅第一次执行）
    [
      TMap.constants.DEFAULT_CONTROL_ID.ZOOM,
      TMap.constants.DEFAULT_CONTROL_ID.SCALE,
      TMap.constants.DEFAULT_CONTROL_ID.ROTATION
    ].forEach((control) => map.removeControl(control));

    // 地图加载完成：标记初始化状态+创建飞线
    map.on("tilesloaded", () => {
      if (!loaded.value) {
        console.log(`${name} 地图加载完成！`);
        isLoading.value = false;
        loaded.value = true; // 标记地图已初始化
        emit("load", true);
        initOrUpdateArcLayer(); // 首次创建飞线
      }
    });
    mapInstance.value = map;
  } catch (error) {
    console.error(`${name} 地图初始化失败:`, error);
    emit("load", false);
    isLoading.value = false;
  }
};

watch(
  () => props.arcData,
  () => {
    if (loaded.value) {
      // 仅当地图已初始化时更新
      startAnimation();
    }
  },
  { deep: true }
);

const pauseAnimation = async () => {
  if (arcLayer.value && arcLayer.value.getData().length > 0) {
    await arcLayer.value.updateAnimation("process", { enable: false });
    await arcLayer.value.setData([]);
    console.log("飞线动画已暂停");
  }
};

const startAnimation = async () => {
  if (arcLayer.value) {
    await arcLayer.value.setData(formatArcData());
    await arcLayer.value.updateAnimation("process", { enable: true });
    console.log("飞线动画已开始");
  }
};

defineExpose({
  // 初始化地图（首次）/ 更新飞线（后续）
  initMap: (name: string) => {
    setTimeout(() => initMap(name), 0);
  },
  pauseAnimation,
  startAnimation
});

// 组件卸载：全销毁（避免内存泄漏）
onUnmounted(() => {
  destroyArcLayer();
  if (mapInstance.value) {
    (mapInstance.value as any)?.destroy();
    mapInstance.value = null;
    loaded.value = false;
  }
});
</script>
