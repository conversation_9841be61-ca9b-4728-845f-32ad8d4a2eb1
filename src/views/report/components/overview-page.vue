<template>
  <div class="overview-page">
    <!-- 上部分：Logo -->
    <div class="overview-page-header">
      <img src="@/assets/icons/icon-bwton-logo.png" alt="logo" class="overview-page-logo" />
    </div>

    <!-- 中部分：标题和描述 -->
    <div class="overview-page-content">
      <h1 class="overview-page-title">{{ title }}</h1>
      <div class="overview-page-title-line"></div>
      <p class="overview-page-description">{{ description }}</p>
    </div>

    <!-- 下部分：日期信息 -->
    <div class="overview-page-footer">
      <span class="overview-page-date">{{ dateRange }}</span>
      <div class="overview-page-date-line"></div>
      <div class="page-button">
          <img src="@/assets/left-circle.png" alt="logo" class="circle-button" @click.stop="changeSlide('prev')" />
          <img src="@/assets/right-circle.png" alt="logo" class="circle-button" @click.stop="changeSlide('next')" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSlideStore } from "@/store/modules/slide";
import { debounce } from "lodash-es";
const slideStore = useSlideStore();

export interface OverviewPageProps {
  title?: string;
  description?: string;
  dateRange?: string;
}

withDefaults(defineProps<OverviewPageProps>(), {
  title: "",
  description: "",
  dateRange: "2025.4-2025.6"
});

const changeSlide = debounce(function (direction: "prev" | "next") {
  if (direction === "prev") {
    slideStore.actionPrevSlide();
  } else {
    slideStore.actionNextSlide();
  }
}, 300);
</script>

<style lang="scss" scoped>
.overview-page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 23px 82px;
  box-sizing: border-box;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;

  .overview-page-header {
    width: 130px;
    height: 58px;

    .overview-page-logo {
      width: 100%;
      height: 100%;
    }
  }


  .overview-page-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 16px;
  }

  .overview-page-title {
    font-family: Alimama ShuHeiTi;
    font-size: 60px;
    font-weight: bold;
    letter-spacing: 0.08em;
    font-variation-settings: "opsz" auto;
    background: linear-gradient(180deg, #E0FFFF 2%, #53FEFF 291%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .overview-page-title-line {
    width: 44px;
    height: 10px;
    margin-top: 36px;
    margin-bottom: 56px;
    background: #00FBFD;
    border-radius: 5px;
  }

  .overview-page-description {
    width: 60%;
    font-size: 28px;
    color: #CDFEFF;
    line-height: 1.6;
    margin: 0;
  }

  .overview-page-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 27px;
  }

  .overview-page-date {
    font-family: DIN Alternate;
    font-size: 22px;
    font-weight: bold;
    line-height: 26px;
    letter-spacing: 0.08em;

    font-variation-settings: "opsz" auto;
    background: linear-gradient(180deg, #D0FFFF 0%, #00FBFD 298%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
  .overview-page-date-line {
    width: calc(100% - 260px);
    height: 1px;
    background: #A0FEFF;
  }
  .page-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 70px;
  }
  .circle-button {
    width: 27px;
    height: 27px;
    cursor: pointer;
  }
}
</style>
