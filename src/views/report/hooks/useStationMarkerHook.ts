// 类型定义
export interface StationDataItem {
  name: string;
  position: {
    lat: number;
    lng: number;
    height?: number;
  };
  popupData?: {
    station_name: string;
    station_flow?: number;
    local_visitor_count?: number;
    non_local_visitor_count?: number;
  };
}

import subwayIcon from "@/assets/subway-icon.png"; // 根据实际目录结构调整路径
import { debounce } from "lodash-es";

export const useStationMarkerHook = () => {
  const multiMarker: Ref<any> = ref(null);
  let markerPopups: Record<string, any> = {};
  const TMap = (window as any).TMap;
  const styleMap: Record<string, any> = {
    stationMarker: new TMap.MarkerStyle({
      width: 32,
      height: 80,
      anchor: { x: 16, y: 80 },
      src: subwayIcon,
      zIndex: 100
    })
  };

  // 标记点创建逻辑（使用 MultiMarker 优化性能）
  const createMarkers = (stationData: StationDataItem[], instance: any) => {
    if (multiMarker.value || markerPopups) {
      destroyMarkers();
    }

    // 标记点数据批量构建
    const geometries: any[] = [];

    stationData.forEach((station: StationDataItem) => {
      geometries.push({
        id: `station-marker-${station.name}`,
        position: new TMap.LatLng(
          station.position.lat,
          station.position.lng,
          station.position.height || 0
        ),
        styleId: "stationMarker"
      });

      createMarkerPopups(station, instance);
    });

    multiMarker.value = new TMap.MultiMarker({
      map: instance,
      styles: styleMap,
      geometries
    });

    // 点击事件统一处理
    multiMarker.value.on("click", (e: any) => {
      const { id } = e.geometry;
      openMarkerPopup(id);
    });
  };

  // 弹窗创建逻辑（自动适配不同数据类型）
  const createMarkerPopups = (item: StationDataItem, instance: any) => {
    if (!item.popupData?.station_name) return;

    const { station_name, station_flow, local_visitor_count, non_local_visitor_count } =
      item.popupData;
    const hasFlowData = station_flow !== undefined;
    const hasResidenceData =
      local_visitor_count !== undefined && non_local_visitor_count !== undefined;

    // 智能判断弹窗内容（客流/户籍分布）
    let popupContent = "";
    if (hasResidenceData) {
      popupContent = `
          <div class="v-station-marker-popup">
            <p class="mb-6 font-bold">${station_name}</p>
            <p class="mb-6">本地客流：<span class="font-bold">${local_visitor_count} </span>万</p>
            <p>外地客流：<span class="font-bold">${non_local_visitor_count} </span>万</p>
          </div>
        `;
    } else if (hasFlowData) {
      popupContent = `
          <div class="v-station-marker-popup">
            <p class="mb-6 font-bold">${station_name}</p>
            <p>客流人数：<span class="font-bold">${station_flow} </span>万</p>
          </div>
        `;
    }

    if (!popupContent) return;

    const popup = new TMap.InfoWindow({
      map: instance,
      id: `station-marker-${item.name}-popup`,
      position: new TMap.LatLng(item.position.lat, item.position.lng, item.position.height || 0),
      enableCustom: true,
      content: popupContent,
      zIndex: 200,
      offset: { x: 0, y: -90 }
    });

    popup.close();
    markerPopups[`station-marker-${item.name}-popup`] = popup;
  };

  // 弹窗交互逻辑
  const openMarkerPopup = debounce(function (id: string) {
    Object.keys(markerPopups).forEach((key) => {
      markerPopups[key][key === `${id}-popup` ? "open" : "close"]();
    });
  }, 300);

  // 清理资源
  const destroyMarkers = () => {
    Object.values(markerPopups).forEach((popup) => popup?.destroy());
    markerPopups = {};
    if (multiMarker.value) {
      multiMarker.value.off("click", () => {});
      multiMarker.value.destroy();
      multiMarker.value = null;
    }
  };

  return {
    createMarkers,
    destroyMarkers
  };
};
