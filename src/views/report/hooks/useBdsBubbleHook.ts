export interface BubbleData {
  bds_id: string;
  value: string;
  size: number;
  position: {
    lat: number;
    lng: number;
  };
}

export const useBdsBubbleHook = () => {
  const TMap = (window as any).TMap;
  let bubbleMaps = {} as { [key: string]: any };
  // 气泡创建逻辑
  const createBubble = (instance: any, data: BubbleData[] = []) => {
    data.forEach((item: BubbleData, index: number) => {
      const themeClass = ["top-one", "top-two", "top-three"][index] ?? "";
      const infoContent = `
                <div class="v-bds-bubble-window ${themeClass}" style="width: ${item.size}px; height: ${item.size}px;"> 
                  <p class="text-center">${item.value}</p>
                </div>
              `;

      bubbleMaps[item.bds_id] = new TMap.InfoWindow({
        map: instance,
        id: item.bds_id,
        position: new TMap.LatLng(item.position.lat, item.position.lng),
        enableCustom: true,
        content: infoContent,
        zIndex: 1,
        offset: { x: 0, y: -24 }
      }).open();
    });
  };

  const destoryBubble = () => {
    Object.values(bubbleMaps).forEach((item: any) => {
      item?.close();
      item?.destroy();
    });
    bubbleMaps = {} as { [key: string]: any };
  };

  return {
    createBubble,
    destoryBubble
  };
};
