/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AgePieChart: typeof import('./views/report/components/agePieChart.vue')['default']
    ArcMap: typeof import('./views/report/components/arc-map.vue')['default']
    BarChart: typeof import('./views/report/components/barChart.vue')['default']
    BarLineChart: typeof import('./views/report/components/bar-line-chart.vue')['default']
    BdsMap: typeof import('./views/report/components/bds-map.vue')['default']
    BubbleChart: typeof import('./views/report/slides/trends-forecasts/components/bubble-chart.vue')['default']
    BusinessDistrictTags: typeof import('./views/report/components/business-district-tags.vue')['default']
    CardSlot: typeof import('./views/report/components/card-slot.vue')['default']
    ConcentricPieChart: typeof import('./views/report/components/concentric-pie-chart.vue')['default']
    copy: typeof import('./views/report/slides/trends-forecasts/components/line-chart copy.vue')['default']
    DistrictSelector: typeof import('./views/report/components/district-selector.vue')['default']
    EchartsBarChart: typeof import('./views/report/components/echartsBarChart.vue')['default']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    GenderPieChart: typeof import('./views/report/components/genderPieChart.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    LayoutFooter: typeof import('./views/report/components/layout-footer.vue')['default']
    LayoutHeader: typeof import('./views/report/components/layout-header.vue')['default']
    LayoutMain: typeof import('./views/report/components/layout-main.vue')['default']
    LineChart: typeof import('./views/report/slides/trends-forecasts/components/line-chart.vue')['default']
    LineChartSmall: typeof import('./views/report/slides/trends-forecasts/components/line-chart-small.vue')['default']
    MapComponent: typeof import('./views/report/components/map-component.vue')['default']
    OverviewPage: typeof import('./views/report/components/overview-page.vue')['default']
    PublicStationMap: typeof import('./views/report/slides/trends-forecasts/components/public-station-map.vue')['default']
    RingNoLine: typeof import('./views/report/slides/trends-forecasts/components/ring-no-line.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StayDurationChart: typeof import('./views/report/slides/trends-forecasts/components/stay-duration-chart.vue')['default']
    SummaryPage: typeof import('./views/report/components/summary-page.vue')['default']
    SummaryPageDemo: typeof import('./views/report/components/summary-page-demo.vue')['default']
    TimePreferenceChart: typeof import('./views/report/slides/consumer-preference-analysis/components/time-preference-chart.vue')['default']
  }
}
