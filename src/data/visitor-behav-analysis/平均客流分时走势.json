{"result": {"page_title_desc": "平均客流分时走势", "page_desc": "各商圈到访人数分时统计", "page_conclusion": "苏州商圈客流呈现双峰分布特征，早高峰（8-9时）与晚高峰（17-19时）承载压力显著；建议优化高峰时段服务资源配置，并针对夜经济商圈（如阳澄）延长运营时间释放消费潜力。", "map_data_intro": "", "map_data_desc": "", "bds_list": [{"bds_id": "bds4", "bds_name": "苏州中心", "bds_conclusion": "", "average_flow": 31827, "hour_list": [{"hour": 0, "flow_h": 2022}, {"hour": 1, "flow_h": 2052}, {"hour": 2, "flow_h": 1694}, {"hour": 3, "flow_h": 1508}, {"hour": 4, "flow_h": 1419}, {"hour": 5, "flow_h": 1698}, {"hour": 6, "flow_h": 5640}, {"hour": 7, "flow_h": 15498}, {"hour": 8, "flow_h": 28936}, {"hour": 9, "flow_h": 29884}, {"hour": 10, "flow_h": 35859}, {"hour": 11, "flow_h": 43516}, {"hour": 12, "flow_h": 49954}, {"hour": 13, "flow_h": 52174}, {"hour": 14, "flow_h": 52408}, {"hour": 15, "flow_h": 53717}, {"hour": 16, "flow_h": 56461}, {"hour": 17, "flow_h": 63493}, {"hour": 18, "flow_h": 70229}, {"hour": 19, "flow_h": 68703}, {"hour": 20, "flow_h": 60648}, {"hour": 21, "flow_h": 41433}, {"hour": 22, "flow_h": 20134}, {"hour": 23, "flow_h": 4768}]}, {"bds_id": "bds5", "bds_name": "星海广场", "bds_conclusion": "", "average_flow": 4515, "hour_list": [{"hour": 0, "flow_h": 519}, {"hour": 1, "flow_h": 556}, {"hour": 2, "flow_h": 494}, {"hour": 3, "flow_h": 427}, {"hour": 4, "flow_h": 407}, {"hour": 5, "flow_h": 446}, {"hour": 6, "flow_h": 990}, {"hour": 7, "flow_h": 2462}, {"hour": 8, "flow_h": 5712}, {"hour": 9, "flow_h": 6123}, {"hour": 10, "flow_h": 6662}, {"hour": 11, "flow_h": 7652}, {"hour": 12, "flow_h": 8261}, {"hour": 13, "flow_h": 7872}, {"hour": 14, "flow_h": 7692}, {"hour": 15, "flow_h": 7825}, {"hour": 16, "flow_h": 7725}, {"hour": 17, "flow_h": 8741}, {"hour": 18, "flow_h": 8657}, {"hour": 19, "flow_h": 6934}, {"hour": 20, "flow_h": 5479}, {"hour": 21, "flow_h": 3777}, {"hour": 22, "flow_h": 2122}, {"hour": 23, "flow_h": 813}]}, {"bds_id": "bds6", "bds_name": "李公堤", "bds_conclusion": "", "average_flow": 2026, "hour_list": [{"hour": 0, "flow_h": 394}, {"hour": 1, "flow_h": 432}, {"hour": 2, "flow_h": 354}, {"hour": 3, "flow_h": 305}, {"hour": 4, "flow_h": 266}, {"hour": 5, "flow_h": 294}, {"hour": 6, "flow_h": 684}, {"hour": 7, "flow_h": 1656}, {"hour": 8, "flow_h": 2514}, {"hour": 9, "flow_h": 2579}, {"hour": 10, "flow_h": 2535}, {"hour": 11, "flow_h": 2822}, {"hour": 12, "flow_h": 2970}, {"hour": 13, "flow_h": 2980}, {"hour": 14, "flow_h": 2892}, {"hour": 15, "flow_h": 3070}, {"hour": 16, "flow_h": 3269}, {"hour": 17, "flow_h": 3931}, {"hour": 18, "flow_h": 4165}, {"hour": 19, "flow_h": 3591}, {"hour": 20, "flow_h": 3090}, {"hour": 21, "flow_h": 2146}, {"hour": 22, "flow_h": 1166}, {"hour": 23, "flow_h": 508}]}, {"bds_id": "bds3", "bds_name": "圆融广场", "bds_conclusion": "", "average_flow": 2615, "hour_list": [{"hour": 0, "flow_h": 232}, {"hour": 1, "flow_h": 261}, {"hour": 2, "flow_h": 258}, {"hour": 3, "flow_h": 251}, {"hour": 4, "flow_h": 254}, {"hour": 5, "flow_h": 326}, {"hour": 6, "flow_h": 750}, {"hour": 7, "flow_h": 1674}, {"hour": 8, "flow_h": 3150}, {"hour": 9, "flow_h": 3236}, {"hour": 10, "flow_h": 3745}, {"hour": 11, "flow_h": 4266}, {"hour": 12, "flow_h": 4498}, {"hour": 13, "flow_h": 4408}, {"hour": 14, "flow_h": 4296}, {"hour": 15, "flow_h": 4222}, {"hour": 16, "flow_h": 4481}, {"hour": 17, "flow_h": 4980}, {"hour": 18, "flow_h": 5122}, {"hour": 19, "flow_h": 4447}, {"hour": 20, "flow_h": 3726}, {"hour": 21, "flow_h": 2688}, {"hour": 22, "flow_h": 1119}, {"hour": 23, "flow_h": 357}]}, {"bds_id": "bds2", "bds_name": "时代广场", "bds_conclusion": "", "average_flow": 4938, "hour_list": [{"hour": 0, "flow_h": 621}, {"hour": 1, "flow_h": 646}, {"hour": 2, "flow_h": 609}, {"hour": 3, "flow_h": 550}, {"hour": 4, "flow_h": 518}, {"hour": 5, "flow_h": 604}, {"hour": 6, "flow_h": 1160}, {"hour": 7, "flow_h": 2832}, {"hour": 8, "flow_h": 6071}, {"hour": 9, "flow_h": 6093}, {"hour": 10, "flow_h": 6914}, {"hour": 11, "flow_h": 8155}, {"hour": 12, "flow_h": 8925}, {"hour": 13, "flow_h": 8544}, {"hour": 14, "flow_h": 8121}, {"hour": 15, "flow_h": 8242}, {"hour": 16, "flow_h": 8619}, {"hour": 17, "flow_h": 9689}, {"hour": 18, "flow_h": 9476}, {"hour": 19, "flow_h": 7889}, {"hour": 20, "flow_h": 6575}, {"hour": 21, "flow_h": 4587}, {"hour": 22, "flow_h": 2182}, {"hour": 23, "flow_h": 875}]}, {"bds_id": "bds1", "bds_name": "文化博览中心", "bds_conclusion": "", "average_flow": 2177, "hour_list": [{"hour": 0, "flow_h": 135}, {"hour": 1, "flow_h": 135}, {"hour": 2, "flow_h": 108}, {"hour": 3, "flow_h": 92}, {"hour": 4, "flow_h": 91}, {"hour": 5, "flow_h": 114}, {"hour": 6, "flow_h": 543}, {"hour": 7, "flow_h": 1998}, {"hour": 8, "flow_h": 4265}, {"hour": 9, "flow_h": 2772}, {"hour": 10, "flow_h": 2377}, {"hour": 11, "flow_h": 2432}, {"hour": 12, "flow_h": 2713}, {"hour": 13, "flow_h": 2881}, {"hour": 14, "flow_h": 2883}, {"hour": 15, "flow_h": 3081}, {"hour": 16, "flow_h": 3324}, {"hour": 17, "flow_h": 5146}, {"hour": 18, "flow_h": 5660}, {"hour": 19, "flow_h": 4534}, {"hour": 20, "flow_h": 3531}, {"hour": 21, "flow_h": 2262}, {"hour": 22, "flow_h": 954}, {"hour": 23, "flow_h": 208}]}, {"bds_id": "bds8", "bds_name": "奥体中心", "bds_conclusion": "", "average_flow": 5305, "hour_list": [{"hour": 0, "flow_h": 305}, {"hour": 1, "flow_h": 308}, {"hour": 2, "flow_h": 274}, {"hour": 3, "flow_h": 264}, {"hour": 4, "flow_h": 281}, {"hour": 5, "flow_h": 454}, {"hour": 6, "flow_h": 1262}, {"hour": 7, "flow_h": 3808}, {"hour": 8, "flow_h": 6282}, {"hour": 9, "flow_h": 5847}, {"hour": 10, "flow_h": 6367}, {"hour": 11, "flow_h": 7156}, {"hour": 12, "flow_h": 7827}, {"hour": 13, "flow_h": 7820}, {"hour": 14, "flow_h": 7432}, {"hour": 15, "flow_h": 7932}, {"hour": 16, "flow_h": 8394}, {"hour": 17, "flow_h": 10284}, {"hour": 18, "flow_h": 12105}, {"hour": 19, "flow_h": 11539}, {"hour": 20, "flow_h": 10207}, {"hour": 21, "flow_h": 7372}, {"hour": 22, "flow_h": 3139}, {"hour": 23, "flow_h": 644}]}, {"bds_id": "bds7", "bds_name": "阳澄", "bds_conclusion": "", "average_flow": 6582, "hour_list": [{"hour": 0, "flow_h": 736}, {"hour": 1, "flow_h": 820}, {"hour": 2, "flow_h": 731}, {"hour": 3, "flow_h": 744}, {"hour": 4, "flow_h": 814}, {"hour": 5, "flow_h": 1150}, {"hour": 6, "flow_h": 2474}, {"hour": 7, "flow_h": 4900}, {"hour": 8, "flow_h": 6946}, {"hour": 9, "flow_h": 7749}, {"hour": 10, "flow_h": 8596}, {"hour": 11, "flow_h": 9909}, {"hour": 12, "flow_h": 10648}, {"hour": 13, "flow_h": 10307}, {"hour": 14, "flow_h": 9513}, {"hour": 15, "flow_h": 9635}, {"hour": 16, "flow_h": 10454}, {"hour": 17, "flow_h": 11931}, {"hour": 18, "flow_h": 12883}, {"hour": 19, "flow_h": 13293}, {"hour": 20, "flow_h": 11642}, {"hour": 21, "flow_h": 7811}, {"hour": 22, "flow_h": 3327}, {"hour": 23, "flow_h": 947}]}], "annotation_list": [{"name": "时刻客流", "desc": "统计指定时间范围内，出现在商圈围栏内，停留时长大于等于15min的设备，按日按小时去重后，按小时分组取均值。"}, {"name": "平均客流", "desc": "各时刻客流的均值。"}]}}